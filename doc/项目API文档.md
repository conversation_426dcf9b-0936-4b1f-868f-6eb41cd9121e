# 项目上传服务 API 设计文档

> **版本**：v1.3.1
> **最后更新**：2025-08-15
> **服务路径**：`/api/v1/projects`
> **技术栈**：FastAPI + Tortoise-ORM + SQLite

---

## 📋 目录

1. [概述](#1-概述)
2. [认证与安全](#2-认证与安全)
3. [数据模型](#3-数据模型)
4. [API 接口](#4-api-接口)
5. [错误处理](#5-错误处理)
6. [业务流程](#6-业务流程)
7. [配置部署](#7-配置部署)
8. [测试示例](#8-测试示例)

---

## 1. 概述

项目上传服务提供统一的项目资源管理能力，支持三种项目类型的创建和管理：

### 📁 支持的项目类型

| 类型 | 说明 | 适用场景 |
|-----|------|----------|
| **文件项目** | 上传工程压缩包或源文件 | 完整的爬虫项目、数据处理脚本 |
| **规则项目** | 配置网页采集规则 | 简单的网页数据抓取任务 |
| **代码项目** | 直接上传源码内容 | 快速部署单文件脚本 |

### 🎯 核心功能

- 多种上传方式支持
- 自动依赖管理
- 文件安全存储
- 项目版本控制
- 标签化分类管理

---

## 2. 认证与安全

### 🔐 身份认证

```http
Authorization: Bearer <JWT_TOKEN>
```

**JWT 配置**：
- 算法：HMAC-SHA256
- 库：PyJWT
- 过期时间：24小时（可配置）

### 🛡️ 安全措施

| 安全项 | 实现方式 |
|--------|----------|
| **文件存储** | UUID 重命名 + MD5 校验 |
| **路径安全** | 防止路径穿越攻击 |
| **文件类型** | 白名单验证 |
| **大小限制** | 最大 100MB（可配置） |
| **权限控制** | 基于用户角色的访问控制 |

---

## 3. 数据模型

### 3.1 枚举定义

```python
from enum import Enum

class ProjectType(str, Enum):
    """项目类型"""
    FILE = "file"          # 文件项目
    RULE = "rule"          # 规则项目
    CODE = "code"          # 代码项目

class ProjectStatus(str, Enum):
    """项目状态"""
    DRAFT = "draft"        # 草稿
    ACTIVE = "active"      # 活跃
    INACTIVE = "inactive"  # 非活跃
    ARCHIVED = "archived"  # 已归档

class CrawlEngine(str, Enum):
    """采集引擎"""
    BROWSER = "browser"                  # 浏览器引擎（Selenium/Playwright等）
    REQUESTS = "requests"                # Requests HTTP库
    CURL_CFFI = "curl_cffi"             # curl_cffi库（模拟curl请求）

class PaginationType(str, Enum):
    """翻页类型"""
    URL_PARAM = "url_param"              # URL参数翻页
    JAVASCRIPT = "javascript"            # JS点击翻页
    AJAX = "ajax"                        # AJAX加载
    INFINITE_SCROLL = "infinite_scroll"  # 无限滚动
```

### 3.2 项目主表

```python
class Project(models.Model):
    """项目主表"""
    id = fields.BigIntField(pk=True)
    name = fields.CharField(max_length=255, unique=True, description="项目名称")
    description = fields.TextField(null=True, description="项目描述")
    type = fields.CharEnumField(ProjectType, description="项目类型")
    status = fields.CharEnumField(ProjectStatus, default=ProjectStatus.DRAFT)

    # 标签和依赖
    tags = fields.JSONField(default=list, description="项目标签")
    dependencies = fields.JSONField(null=True, description="Python依赖包")

    # 时间和用户信息
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    updated_by = fields.BigIntField(null=True, description="更新者ID")

    # 关联关系
    user = fields.ForeignKeyField("models.User", related_name="projects", description="创建者")

    # 统计信息
    download_count = fields.IntField(default=0, description="下载次数")
    star_count = fields.IntField(default=0, description="收藏次数")

    class Meta:
        table = "projects"
        indexes = [("name",), ("type",), ("status",), ("user",)]
```

### 3.3 文件项目详情

```python
class ProjectFile(models.Model):
    """文件项目详情"""
    id = fields.BigIntField(pk=True)
    project = fields.OneToOneField("models.Project", related_name="file_detail")

    # 文件信息
    file_path = fields.CharField(max_length=500, description="存储路径")
    original_name = fields.CharField(max_length=255, description="原始文件名")
    file_size = fields.BigIntField(description="文件大小(字节)")
    file_type = fields.CharField(max_length=50, description="文件类型")
    file_hash = fields.CharField(max_length=64, description="MD5哈希")

    # 执行配置
    entry_point = fields.CharField(max_length=255, null=True, description="入口文件")
    runtime_config = fields.JSONField(null=True, description="运行时配置")
    environment_vars = fields.JSONField(null=True, description="环境变量")

    # 存储配置
    storage_type = fields.CharField(max_length=20, default="local")
    is_compressed = fields.BooleanField(default=False)
    compression_ratio = fields.FloatField(null=True, description="压缩比")

    class Meta:
        table = "project_files"
```

### 3.4 规则项目详情

```python
class ProjectRule(models.Model):
    """规则项目详情"""
    id = fields.BigIntField(pk=True)
    project = fields.OneToOneField("models.Project", related_name="rule_detail")

    # 基础配置
    engine = fields.CharEnumField(CrawlEngine, default=CrawlEngine.REQUESTS)
    target_url = fields.CharField(max_length=2000, description="目标URL")
    url_pattern = fields.CharField(max_length=500, null=True, description="URL匹配模式")

    # 抓取规则
    list_selectors = fields.JSONField(null=True, description="列表页选择器")
    detail_selectors = fields.JSONField(null=True, description="详情页选择器")
    data_schema = fields.JSONField(null=True, description="数据结构定义")

    # 翻页配置
    pagination_type = fields.CharEnumField(PaginationType, null=True)
    pagination_rule = fields.CharField(max_length=500, null=True)
    max_pages = fields.IntField(default=10)

    # 请求配置
    request_delay = fields.IntField(default=1000, description="请求间隔(ms)")
    retry_count = fields.IntField(default=3)
    timeout = fields.IntField(default=30, description="超时时间(s)")

    # 高级配置
    headers = fields.JSONField(null=True, description="请求头")
    cookies = fields.JSONField(null=True, description="Cookie")
    proxy_config = fields.JSONField(null=True, description="代理配置")
    anti_spider = fields.JSONField(null=True, description="反爬虫配置")

    class Meta:
        table = "project_rules"
```

### 3.5 代码项目详情

```python
class ProjectCode(models.Model):
    """代码项目详情"""
    id = fields.BigIntField(pk=True)
    project = fields.OneToOneField("models.Project", related_name="code_detail")

    # 代码信息
    content = fields.TextField(description="代码内容")
    language = fields.CharField(max_length=50, default="python")
    version = fields.CharField(max_length=20, default="1.0.0")
    content_hash = fields.CharField(max_length=64, description="内容哈希")

    # 执行配置
    entry_point = fields.CharField(max_length=255, null=True)
    runtime_config = fields.JSONField(null=True)
    environment_vars = fields.JSONField(null=True)

    # 文档信息
    documentation = fields.TextField(null=True)
    changelog = fields.TextField(null=True, description="变更日志")

    class Meta:
        table = "project_codes"
```

---

## 4. API 接口

### 4.1 认证接口

#### 4.1.1 用户登录

**基本信息**

```http
POST /api/v1/auth/login
Content-Type: application/json
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `username` | string | ✅ | 用户名 |
| `password` | string | ✅ | 密码 |

**请求示例**

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin"
  }'
```

**成功响应**

```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user_id": 1,
  "username": "admin",
  "is_admin": true
}
```

**默认管理员账号**：
- 用户名：`admin`
- 密码：`admin`

**令牌使用**：
在后续API请求中，需要在Header中添加：
```http
Authorization: Bearer <access_token>
```

### 4.2 项目管理接口

#### 4.2.1 创建项目

#### 基本信息

```http
POST /api/v1/projects
Content-Type: multipart/form-data
Authorization: Bearer <token>
```

#### 通用参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `name` | string | ✅ | 项目名称（3-50字符，唯一） |
| `description` | string | ❌ | 项目描述（最长500字符） |
| `type` | string | ✅ | 项目类型：`file`/`rule`/`code` |
| `tags` | string | ❌ | 标签，逗号分隔或JSON数组 |
| `dependencies` | string | ❌ | Python依赖包JSON数组 |

#### 4.2.2 文件项目参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `file` | file | ✅ | 项目文件（.zip/.tar.gz/.py） |
| `entry_point` | string | ❌ | 入口文件路径 |
| `runtime_config` | string | ❌ | 运行时配置JSON |
| `environment_vars` | string | ❌ | 环境变量JSON |

**示例请求**：

```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=WebCrawler" \
  -F "description=新闻网站爬虫项目" \
  -F "type=file" \
  -F "tags=crawler,news,python" \
  -F "file=@project.zip" \
  -F "entry_point=main.py" \
  -F 'runtime_config={"max_workers": 4}' \
  -F 'dependencies=["requests", "beautifulsoup4", "lxml"]'
```

#### 4.2.3 规则项目参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `engine` | string | ❌ | 采集引擎，默认`requests` |
| `target_url` | string | ✅ | 目标网页URL |
| `url_pattern` | string | ❌ | URL匹配正则表达式 |
| `list_selectors` | string | ❌ | 列表页选择器JSON |
| `detail_selectors` | string | ✅ | 详情页选择器JSON |
| `pagination_type` | string | ❌ | 翻页类型 |
| `pagination_rule` | string | ❌ | 翻页规则 |
| `max_pages` | integer | ❌ | 最大页数，默认10 |
| `request_delay` | integer | ❌ | 请求间隔ms，默认1000 |

**采集引擎说明**：

| 引擎 | 值 | 适用场景 | 特点 |
|------|-----|----------|------|
| **Requests** | `requests` | 静态网页、API接口 | 轻量快速，资源消耗低，适合大批量采集 ⭐ 默认 |
| **浏览器引擎** | `browser` | SPA应用、需要JavaScript的网页 | 支持JavaScript渲染，可处理复杂交互 |
| **curl_cffi** | `curl_cffi` | 有反爬虫机制的网站 | 模拟真实curl请求，反检测能力强 |

**引擎选择建议**：
- 新闻网站、博客 → `requests`
- React/Vue应用 → `browser`
- 电商网站、有反爬虫的网站 → `curl_cffi`

**不同引擎示例**：

```bash
# 使用 requests 引擎采集新闻网站
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=NewsCollector" \
  -F "type=rule" \
  -F "engine=requests" \
  -F "target_url=https://news.example.com" \
  -F 'detail_selectors={"title": "h1", "content": ".article-content"}'

# 使用 browser 引擎采集SPA应用
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=SPACollector" \
  -F "type=rule" \
  -F "engine=browser" \
  -F "target_url=https://spa.example.com" \
  -F 'detail_selectors={"title": "h1", "data": ".dynamic-content"}'

# 使用 curl_cffi 引擎绕过反爬虫
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=AntiSpiderCollector" \
  -F "type=rule" \
  -F "engine=curl_cffi" \
  -F "target_url=https://protected.example.com" \
  -F 'detail_selectors={"title": "h1", "price": ".price"}'
```

**选择器配置示例**：

```json
{
  "list_selectors": [
    {
      "name": "article_links",
      "selector": "//div[@class='article-list']//a/@href",
      "type": "xpath"
    }
  ],
  "detail_selectors": [
    {
      "field": "title",
      "selector": "//h1[@class='title']/text()",
      "type": "xpath",
      "required": true
    },
    {
      "field": "content",
      "selector": ".content",
      "type": "css",
      "required": true
    },
    {
      "field": "publish_time",
      "selector": "//span[@class='time']/@datetime",
      "type": "xpath",
      "data_type": "datetime"
    }
  ]
}
```

#### 4.2.4 代码项目参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `code_file` | file | ✅ | 代码文件 |
| `language` | string | ❌ | 编程语言，默认`python` |
| `version` | string | ❌ | 版本号，默认`1.0.0` |
| `entry_point` | string | ❌ | 入口函数 |
| `documentation` | string | ❌ | 代码文档 |

### 4.3 响应格式

#### 成功响应

```json
{
  "success": true,
  "code": 200,
  "message": "项目创建成功",
  "data": {
    "id": 12345,
    "name": "WebCrawler",
    "type": "file",
    "status": "draft",
    "created_at": "2025-06-02T10:30:00Z",
    "created_by": 1001,
    "tags": ["crawler", "news", "python"],
    "file_info": {
      "original_name": "project.zip",
      "file_size": 2048576,
      "file_hash": "d41d8cd98f00b204e9800998ecf8427e"
    }
  },
  "timestamp": "2025-06-02T10:30:00Z"
}
```

#### 错误响应

```json
{
  "success": false,
  "code": 400,
  "message": "参数验证失败",
  "errors": [
    {
      "field": "name",
      "message": "项目名称不能为空"
    },
    {
      "field": "file",
      "message": "请上传项目文件"
    }
  ],
  "timestamp": "2025-06-02T10:30:00Z"
}
```

### 4.4 其他项目接口

#### 4.4.1 获取项目列表

```http
GET /api/v1/projects?page=1&size=20&type=file&status=active&tag=crawler
Authorization: Bearer <token>
```

**查询参数**：
- `page`: 页码，从1开始
- `size`: 每页数量，最大100
- `type`: 项目类型筛选 (file/rule/code)
- `status`: 项目状态筛选 (draft/active/inactive/archived)
- `tag`: 标签筛选

#### 4.4.2 获取项目详情

```http
GET /api/v1/projects/{project_id}
Authorization: Bearer <token>
```

**权限说明**：用户只能查看自己创建的项目。

#### 4.4.3 更新项目

```http
PUT /api/v1/projects/{project_id}
Content-Type: application/json
Authorization: Bearer <token>
```

**可更新字段**：name, description, status, tags, dependencies

#### 4.4.4 删除项目

```http
DELETE /api/v1/projects/{project_id}
Authorization: Bearer <token>
```

**注意**：删除操作不可逆，会同时删除相关文件和配置。

---

## 5. 错误处理

### 5.1 HTTP 状态码

| 状态码 | 说明 | 场景 |
|--------|------|------|
| 200 | 成功 | 请求处理成功 |
| 400 | 参数错误 | 请求参数验证失败 |
| 401 | 未授权 | JWT token无效或过期 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 项目不存在 |
| 409 | 资源冲突 | 项目名称重复 |
| 413 | 文件过大 | 超出大小限制 |
| 415 | 文件类型不支持 | 不在白名单内 |
| 422 | 业务逻辑错误 | 依赖包格式错误 |
| 500 | 服务器错误 | 内部错误 |

### 5.2 业务错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| `INVALID_PROJECT_NAME` | 项目名称不符合规范 | 检查名称长度和字符 |
| `DUPLICATE_PROJECT_NAME` | 项目名称已存在 | 使用唯一的项目名称 |
| `INVALID_FILE_TYPE` | 文件类型不支持 | 上传支持的文件格式 |
| `FILE_TOO_LARGE` | 文件大小超限 | 压缩文件后重新上传 |
| `INVALID_DEPENDENCIES` | 依赖格式错误 | 检查JSON格式 |
| `INVALID_URL` | URL格式错误 | 提供有效的HTTP(S) URL |
| `INVALID_SELECTOR` | 选择器语法错误 | 检查XPath或CSS选择器 |
| `STORAGE_ERROR` | 文件存储失败 | 稍后重试或联系管理员 |

---

## 6. 业务流程

### 6.1 处理流程图

```mermaid
graph TD
    A[客户端请求] --> B[JWT认证]
    B --> C[参数验证]
    C --> D[文件处理]
    D --> E[数据入库]
    E --> F[响应返回]

    C --> G{验证失败?}
    G -->|是| H[返回400错误]

    D --> I{文件处理失败?}
    I -->|是| J[返回500错误]

    E --> K{数据库错误?}
    K -->|是| L[回滚并返回500错误]
```

### 6.2 存储策略

#### 文件存储路径规则

```
/{storage_root}/projects/{year}/{month}/{day}/{uuid}.{ext}
```

**示例**：

```
/var/storage/projects/2025/06/02/a1b2c3d4-e5f6-7890-abcd-ef1234567890.zip
```

#### 存储配置

| 配置项 | 本地存储 | 对象存储 |
|--------|----------|----------|
| **路径** | `/var/storage` | `s3://bucket-name` |
| **备份** | 定期备份到远程 | 自带高可用 |
| **访问** | 文件系统 | HTTP URL |
| **权限** | 文件权限 | IAM策略 |

---

## 7. 配置部署

### 7.1 环境配置

```python
# config/settings.py
from pydantic import BaseSettings

class Settings(BaseSettings):
    # 数据库配置
    DATABASE_URL: str = "sqlite://./antcode.sqlite3"

    # 存储配置
    STORAGE_TYPE: str = "local"  # local | s3 | oss
    LOCAL_STORAGE_PATH: str = "/var/storage/projects"

    # S3配置
    AWS_ACCESS_KEY_ID: str = ""
    AWS_SECRET_ACCESS_KEY: str = ""
    AWS_S3_BUCKET: str = ""
    AWS_S3_REGION: str = "us-east-1"

    # 文件限制
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_FILE_TYPES: list = [
        '.zip', '.tar.gz', '.py', '.txt', '.json'
    ]

    # JWT配置
    JWT_SECRET_KEY: str = "your-secret-key-here"
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_HOURS: int = 24

    # Redis配置(缓存/队列)
    REDIS_URL: str = "redis://localhost:6379"

    class Config:
        env_file = ".env"
```

### 7.2 Docker 部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///app/data/antcode.sqlite3
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    volumes:
      - ./storage:/var/storage
      - ./data:/app/data

  redis:
    image: redis:alpine

volumes:
  storage_data:
  database_data:
```

### 7.3 数据库迁移

```bash
# 安装aerich
pip install aerich

# 初始化
aerich init -t app.database.TORTOISE_ORM

# 生成迁移文件
aerich init-db

# 执行迁移
aerich upgrade

# 生成新的迁移
aerich migrate

# 查看迁移历史
aerich history
```

---

## 8. 测试示例

### 8.1 单元测试

```python
import pytest
from httpx import AsyncClient
from app.main import app

class TestProjectAPI:

    @pytest.mark.asyncio
    async def test_create_file_project(self):
        """测试创建文件项目"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            files = {
                "file": ("test.py", b"print('hello world')", "text/plain")
            }
            data = {
                "name": "TestProject",
                "type": "file",
                "description": "测试项目",
                "tags": "test,python",
                "dependencies": '["requests", "pytest"]'
            }
            headers = {"Authorization": "Bearer test_token"}

            response = await client.post(
                "/api/v1/projects",
                files=files,
                data=data,
                headers=headers
            )

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["data"]["name"] == "TestProject"

    @pytest.mark.asyncio
    async def test_create_rule_project(self):
        """测试创建规则项目"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            data = {
                "name": "NewsRule",
                "type": "rule",
                "engine": "requests",
                "target_url": "https://news.example.com",
                "detail_selectors": json.dumps([
                    {"field": "title", "selector": "//h1", "type": "xpath"}
                ])
            }
            headers = {"Authorization": "Bearer test_token"}

            response = await client.post("/api/v1/projects", data=data, headers=headers)
            assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_invalid_project_name(self):
        """测试无效项目名称"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            data = {"name": "ab", "type": "file"}  # 名称太短
            headers = {"Authorization": "Bearer test_token"}

            response = await client.post("/api/v1/projects", data=data, headers=headers)
            assert response.status_code == 400
```

### 8.2 集成测试

```python
@pytest.mark.asyncio
async def test_project_workflow():
    """测试完整的项目工作流程"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        headers = {"Authorization": "Bearer test_token"}

        # 1. 创建项目
        files = {"file": ("script.py", b"print('test')", "text/plain")}
        data = {"name": "WorkflowTest", "type": "file"}

        create_response = await client.post(
            "/api/v1/projects", files=files, data=data, headers=headers
        )
        assert create_response.status_code == 200
        project_id = create_response.json()["data"]["id"]

        # 2. 获取项目详情
        detail_response = await client.get(
            f"/api/v1/projects/{project_id}", headers=headers
        )
        assert detail_response.status_code == 200

        # 3. 更新项目
        update_data = {"description": "Updated description"}
        update_response = await client.put(
            f"/api/v1/projects/{project_id}",
            json=update_data,
            headers=headers
        )
        assert update_response.status_code == 200

        # 4. 删除项目
        delete_response = await client.delete(
            f"/api/v1/projects/{project_id}", headers=headers
        )
        assert delete_response.status_code == 200
```

### 8.3 性能测试

```python
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

async def performance_test():
    """并发创建项目性能测试"""
    async def create_project(i):
        async with AsyncClient(app=app, base_url="http://test") as client:
            data = {
                "name": f"PerfTest{i}",
                "type": "rule",
                "target_url": "https://example.com"
            }
            headers = {"Authorization": "Bearer test_token"}
            start_time = time.time()
            response = await client.post("/api/v1/projects", data=data, headers=headers)
            end_time = time.time()

            return {
                "status_code": response.status_code,
                "response_time": end_time - start_time
            }

    # 并发测试
    tasks = [create_project(i) for i in range(100)]
    results = await asyncio.gather(*tasks)

    # 统计结果
    success_count = sum(1 for r in results if r["status_code"] == 200)
    avg_response_time = sum(r["response_time"] for r in results) / len(results)

    print(f"Success rate: {success_count/100*100}%")
    print(f"Average response time: {avg_response_time:.3f}s")
```

---

## 9. API 文档访问

### 9.1 在线文档

启动应用后，可以通过以下地址访问交互式API文档：

#### Swagger UI
```
http://localhost:8000/docs
```
- 交互式API文档界面
- 支持直接测试API接口
- 自动生成请求示例
- 支持JWT令牌认证

#### ReDoc
```
http://localhost:8000/redoc
```
- 更美观的文档展示
- 适合阅读和分享
- 完整的API规范展示
- 支持搜索和导航

### 9.2 文档特性

- **接口分类**: 按功能模块组织（认证、项目管理）
- **详细描述**: 每个接口都有完整的说明和示例
- **参数验证**: 显示字段类型、长度限制等
- **错误处理**: 详细的错误码和解决方案
- **采集引擎指南**: 三种引擎的选择建议和使用场景

---

## 📚 附录

### A. 常见问题

**Q: 支持哪些文件格式？**
A: 支持 .zip、.tar.gz、.py、.txt、.json、.md、.yml、.yaml 等格式，具体见配置文件。

**Q: 如何选择合适的采集引擎？**
A:
- 静态网页（新闻、博客）→ `requests`
- 动态网页（React/Vue应用）→ `browser`
- 有反爬虫机制的网站 → `curl_cffi`

**Q: 采集引擎有什么区别？**
A:
- `requests`: 轻量快速，适合静态内容，资源消耗低
- `browser`: 支持JavaScript，可处理复杂交互，资源消耗高
- `curl_cffi`: 模拟真实curl请求，反检测能力强，性能中等

**Q: 如何配置反爬虫策略？**
A: 在规则项目的 `anti_spider` 字段中配置用户代理轮换、请求间隔、代理等，或选择 `curl_cffi` 引擎。

**Q: 项目名称有什么限制？**
A: 3-50个字符，支持中英文、数字、下划线，需全局唯一。

**Q: 默认管理员账号是什么？**
A: 用户名：`admin`，密码：`admin`。建议生产环境中修改默认密码。