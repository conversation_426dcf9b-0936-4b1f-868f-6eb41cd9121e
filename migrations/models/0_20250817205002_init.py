from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "users" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL /* 用户ID */,
    "username" VARCHAR(50) NOT NULL UNIQUE /* 用户名 */,
    "password_hash" VARCHAR(128) NOT NULL /* 密码哈希 */,
    "email" VARCHAR(100) /* 邮箱 */,
    "is_active" INT NOT NULL DEFAULT 1 /* 是否激活 */,
    "is_admin" INT NOT NULL DEFAULT 0 /* 是否管理员 */,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP /* 创建时间 */,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP /* 更新时间 */,
    "last_login_at" TIMESTAMP /* 最后登录时间 */
) /* 用户表 */;
CREATE TABLE IF NOT EXISTS "projects" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    "name" VARCHAR(255) NOT NULL UNIQUE /* 项目名称 */,
    "description" TEXT /* 项目描述 */,
    "type" VARCHAR(4) NOT NULL /* 项目类型 */,
    "status" VARCHAR(8) NOT NULL DEFAULT 'draft' /* 项目状态 */,
    "tags" JSON NOT NULL /* 项目标签 */,
    "dependencies" JSON /* Python依赖包 */,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP /* 创建时间 */,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP /* 更新时间 */,
    "updated_by" BIGINT /* 更新者ID */,
    "download_count" INT NOT NULL DEFAULT 0 /* 下载次数 */,
    "star_count" INT NOT NULL DEFAULT 0 /* 收藏次数 */,
    "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE /* 创建者 */
) /* 项目主表 */;
CREATE INDEX IF NOT EXISTS "idx_projects_name_7b5b92" ON "projects" ("name");
CREATE INDEX IF NOT EXISTS "idx_projects_type_46042a" ON "projects" ("type");
CREATE INDEX IF NOT EXISTS "idx_projects_status_ad9f12" ON "projects" ("status");
CREATE INDEX IF NOT EXISTS "idx_projects_user_id_5bafbc" ON "projects" ("user_id");
CREATE TABLE IF NOT EXISTS "project_codes" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    "content" TEXT NOT NULL /* 代码内容 */,
    "language" VARCHAR(50) NOT NULL DEFAULT 'python' /* 编程语言 */,
    "version" VARCHAR(20) NOT NULL DEFAULT '1.0.0' /* 版本号 */,
    "content_hash" VARCHAR(64) NOT NULL /* 内容哈希 */,
    "entry_point" VARCHAR(255) /* 入口函数 */,
    "runtime_config" JSON /* 运行时配置 */,
    "environment_vars" JSON /* 环境变量 */,
    "documentation" TEXT /* 代码文档 */,
    "changelog" TEXT /* 变更日志 */,
    "project_id" BIGINT NOT NULL UNIQUE REFERENCES "projects" ("id") ON DELETE CASCADE /* 关联项目 */
) /* 代码项目详情 */;
CREATE TABLE IF NOT EXISTS "project_files" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    "file_path" VARCHAR(500) NOT NULL /* 存储路径 */,
    "original_name" VARCHAR(255) NOT NULL /* 原始文件名 */,
    "file_size" BIGINT NOT NULL /* 文件大小(字节) */,
    "file_type" VARCHAR(50) NOT NULL /* 文件类型 */,
    "file_hash" VARCHAR(64) NOT NULL /* MD5哈希 */,
    "entry_point" VARCHAR(255) /* 入口文件 */,
    "runtime_config" JSON /* 运行时配置 */,
    "environment_vars" JSON /* 环境变量 */,
    "storage_type" VARCHAR(20) NOT NULL DEFAULT 'local' /* 存储类型 */,
    "is_compressed" INT NOT NULL DEFAULT 0 /* 是否压缩 */,
    "compression_ratio" REAL /* 压缩比 */,
    "project_id" BIGINT NOT NULL UNIQUE REFERENCES "projects" ("id") ON DELETE CASCADE /* 关联项目 */
) /* 文件项目详情 */;
CREATE TABLE IF NOT EXISTS "project_rules" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    "engine" VARCHAR(9) NOT NULL DEFAULT 'requests' /* 采集引擎 */,
    "target_url" VARCHAR(2000) NOT NULL /* 目标URL */,
    "url_pattern" VARCHAR(500) /* URL匹配模式 */,
    "callback_type" VARCHAR(6) NOT NULL DEFAULT 'list' /* 回调类型 */,
    "request_method" VARCHAR(6) NOT NULL DEFAULT 'GET' /* 请求方法 */,
    "extraction_rules" JSON /* 提取规则数组 */,
    "list_selectors" JSON /* 列表页选择器（兼容旧版） */,
    "detail_selectors" JSON /* 详情页选择器（兼容旧版） */,
    "data_schema" JSON /* 数据结构定义 */,
    "pagination_config" JSON /* 分页配置JSON */,
    "pagination_type" VARCHAR(15) /* 翻页类型（兼容旧版） */,
    "pagination_rule" VARCHAR(500) /* 翻页规则（兼容旧版） */,
    "max_pages" INT NOT NULL DEFAULT 10 /* 最大页数 */,
    "start_page" INT NOT NULL DEFAULT 1 /* 起始页码 */,
    "request_delay" INT NOT NULL DEFAULT 1000 /* 请求间隔(ms) */,
    "retry_count" INT NOT NULL DEFAULT 3 /* 重试次数 */,
    "timeout" INT NOT NULL DEFAULT 30 /* 超时时间(s) */,
    "priority" INT NOT NULL DEFAULT 0 /* 优先级 */,
    "dont_filter" INT NOT NULL DEFAULT 0 /* 是否去重 */,
    "headers" JSON /* 请求头 */,
    "cookies" JSON /* Cookie */,
    "proxy_config" JSON /* 代理配置 */,
    "anti_spider" JSON /* 反爬虫配置 */,
    "task_config" JSON /* 任务配置（包含task_id模板、worker_id等） */,
    "project_id" BIGINT NOT NULL UNIQUE REFERENCES "projects" ("id") ON DELETE CASCADE /* 关联项目 */
) /* 规则项目详情 */;
CREATE TABLE IF NOT EXISTS "scheduled_tasks" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    "name" VARCHAR(255) NOT NULL UNIQUE /* 任务名称 */,
    "description" TEXT /* 任务描述 */,
    "task_type" VARCHAR(6) NOT NULL /* 任务类型 */,
    "schedule_type" VARCHAR(8) NOT NULL /* 调度类型 */,
    "cron_expression" VARCHAR(100) /* Cron表达式 */,
    "interval_seconds" INT /* 间隔秒数 */,
    "scheduled_time" TIMESTAMP /* 计划执行时间 */,
    "max_instances" INT NOT NULL DEFAULT 1 /* 最大并发实例数 */,
    "timeout_seconds" INT NOT NULL DEFAULT 3600 /* 超时时间(秒) */,
    "retry_count" INT NOT NULL DEFAULT 3 /* 重试次数 */,
    "retry_delay" INT NOT NULL DEFAULT 60 /* 重试延迟(秒) */,
    "status" VARCHAR(9) NOT NULL DEFAULT 'pending' /* PENDING: pending\nRUNNING: running\nSUCCESS: success\nFAILED: failed\nCANCELLED: cancelled\nTIMEOUT: timeout\nPAUSED: paused */,
    "is_active" INT NOT NULL DEFAULT 1 /* 是否激活 */,
    "last_run_time" TIMESTAMP /* 最后运行时间 */,
    "next_run_time" TIMESTAMP /* 下次运行时间 */,
    "failure_count" INT NOT NULL DEFAULT 0 /* 失败次数 */,
    "success_count" INT NOT NULL DEFAULT 0 /* 成功次数 */,
    "execution_params" JSON /* 执行参数 */,
    "environment_vars" JSON /* 环境变量 */,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "project_id" BIGINT NOT NULL REFERENCES "projects" ("id") ON DELETE CASCADE,
    "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE /* 创建者 */
) /* 调度任务表 */;
CREATE INDEX IF NOT EXISTS "idx_scheduled_t_name_a4b51d" ON "scheduled_tasks" ("name");
CREATE INDEX IF NOT EXISTS "idx_scheduled_t_status_994bec" ON "scheduled_tasks" ("status");
CREATE INDEX IF NOT EXISTS "idx_scheduled_t_is_acti_a43d2b" ON "scheduled_tasks" ("is_active");
CREATE TABLE IF NOT EXISTS "task_executions" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    "execution_id" VARCHAR(64) NOT NULL UNIQUE /* 执行ID */,
    "start_time" TIMESTAMP NOT NULL /* 开始时间 */,
    "end_time" TIMESTAMP /* 结束时间 */,
    "duration_seconds" REAL /* 执行时长(秒) */,
    "status" VARCHAR(9) NOT NULL /* 执行状态 */,
    "exit_code" INT /* 退出码 */,
    "error_message" TEXT /* 错误信息 */,
    "log_file_path" VARCHAR(512) /* 日志文件路径 */,
    "error_log_path" VARCHAR(512) /* 错误日志文件路径 */,
    "result_data" JSON /* 结果数据 */,
    "cpu_usage" REAL /* CPU使用率 */,
    "memory_usage" BIGINT /* 内存使用(bytes) */,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "task_id" BIGINT NOT NULL REFERENCES "scheduled_tasks" ("id") ON DELETE CASCADE
) /* 任务执行记录表 */;
CREATE INDEX IF NOT EXISTS "idx_task_execut_executi_9697ff" ON "task_executions" ("execution_id");
CREATE INDEX IF NOT EXISTS "idx_task_execut_status_0f7e0b" ON "task_executions" ("status");
CREATE INDEX IF NOT EXISTS "idx_task_execut_start_t_14b1e5" ON "task_executions" ("start_time");
CREATE TABLE IF NOT EXISTS "task_logs" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    "level" VARCHAR(20) NOT NULL /* 日志级别 */,
    "message" TEXT NOT NULL /* 日志消息 */,
    "timestamp" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "context" JSON /* 上下文信息 */,
    "execution_id" BIGINT NOT NULL REFERENCES "task_executions" ("id") ON DELETE CASCADE
) /* 任务日志表 */;
CREATE INDEX IF NOT EXISTS "idx_task_logs_timesta_a47ec1" ON "task_logs" ("timestamp");
CREATE INDEX IF NOT EXISTS "idx_task_logs_level_72aa79" ON "task_logs" ("level");
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSON NOT NULL
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
