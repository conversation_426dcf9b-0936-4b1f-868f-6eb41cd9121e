# 项目上传服务 API

基于 FastAPI + Tortoise-ORM 实现的项目资源管理服务，支持文件、规则、代码三种项目类型的创建和管理。

## 🚀 快速开始

### 环境要求

- Python 3.11+
- SQLite（默认）或 MySQL

### 安装依赖

```bash
# 使用 uv（推荐）
uv sync

# 或使用 pip
pip install -r requirements.txt
```

### 启动服务

```bash
# 开发模式启动
python main.py

# 或使用 uvicorn
uvicorn src:app --host 0.0.0.0 --port 8000 --reload
```

服务启动后，访问 http://localhost:8000/docs 查看 API 文档。

## 📋 功能特性

### 支持的项目类型

| 类型 | 说明 | 适用场景 |
|-----|------|----------|
| **文件项目** | 上传工程压缩包或源文件 | 完整的爬虫项目、数据处理脚本 |
| **规则项目** | 配置网页采集规则 | 简单的网页数据抓取任务 |
| **代码项目** | 直接上传源码内容 | 快速部署单文件脚本 |

### 核心功能

- ✅ 多种上传方式支持
- ✅ 自动依赖管理
- ✅ 文件安全存储
- ✅ 项目版本控制
- ✅ 标签化分类管理
- ✅ JWT 身份认证
- ✅ 统一错误处理
- ✅ 完整的单元测试

## 🔧 API 接口

### 认证

所有 API 接口都需要 JWT 认证：

```http
Authorization: Bearer <JWT_TOKEN>
```

### 主要接口

| 方法 | 路径 | 说明 |
|------|------|------|
| POST | `/api/v1/projects` | 创建项目 |
| GET | `/api/v1/projects` | 获取项目列表 |
| GET | `/api/v1/projects/{id}` | 获取项目详情 |
| PUT | `/api/v1/projects/{id}` | 更新项目 |
| DELETE | `/api/v1/projects/{id}` | 删除项目 |

### 创建项目示例

#### 文件项目

```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=WebCrawler" \
  -F "description=新闻网站爬虫项目" \
  -F "type=file" \
  -F "tags=crawler,news,python" \
  -F "file=@project.zip" \
  -F "entry_point=main.py"
```

#### 规则项目

```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=NewsRule" \
  -F "type=rule" \
  -F "target_url=https://news.example.com" \
  -F 'detail_selectors=[{"field": "title", "selector": "//h1", "type": "xpath"}]'
```

#### 代码项目

```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=HelloWorld" \
  -F "type=code" \
  -F "language=python" \
  -F "code_file=@hello.py"
```

## 🧪 运行测试

```bash
# 运行所有测试
python run_tests.py

# 或使用 pytest
pytest tests/ -v
```

## 📁 项目结构

```
├── src/                    # 源代码目录
│   ├── api/               # API 路由
│   │   └── v1/           # v1 版本接口
│   ├── core/             # 核心模块
│   │   ├── auth.py       # JWT 认证
│   │   ├── config.py     # 配置管理
│   │   └── exceptions.py # 异常处理
│   ├── models/           # 数据模型
│   │   ├── enums.py      # 枚举定义
│   │   └── project.py    # 项目模型
│   ├── schemas/          # Pydantic 模式
│   │   ├── common.py     # 通用模式
│   │   └── project.py    # 项目模式
│   ├── services/         # 业务逻辑
│   │   ├── file_storage.py    # 文件存储
│   │   └── project_service.py # 项目服务
│   └── utils/            # 工具函数
├── tests/                # 测试文件
├── doc/                  # 文档
├── storage/              # 文件存储目录
├── main.py              # 启动入口
└── README.md            # 项目说明
```

## ⚙️ 配置说明

主要配置项在 `src/core/config.py` 中：

```python
# 存储配置
STORAGE_TYPE = "local"  # local | s3 | oss
LOCAL_STORAGE_PATH = "./storage/projects"

# 文件限制
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
ALLOWED_FILE_TYPES = ['.zip', '.tar.gz', '.py', '.txt', '.json']

# JWT 配置
JWT_SECRET_KEY = "your-secret-key"
JWT_EXPIRE_HOURS = 24
```

## 🔒 安全特性

- JWT 令牌认证
- 文件类型白名单验证
- 文件大小限制
- MD5 文件校验
- UUID 文件重命名
- 路径穿越攻击防护

## 📖 详细文档

更多详细信息请参考：

- [API 设计文档](doc/项目API文档.md)
- [在线 API 文档](http://localhost:8000/docs)（启动服务后访问）

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
