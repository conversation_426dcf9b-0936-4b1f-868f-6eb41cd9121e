# src/services/task_executor.py (确保包含规则项目执行逻辑)
"""任务执行器"""
import asyncio
import json
import subprocess
import tempfile
import os
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import psutil
from loguru import logger

from src.models import Project, ProjectFile, ProjectRule, ProjectCode
from src.models.enums import ProjectType
from src.services.redis_task_service import redis_task_service  # 确保导入


class TaskExecutor:
    """任务执行器"""

    def __init__(self):
        self.running_processes: Dict[str, subprocess.Popen] = {}

    async def execute(
            self,
            project: Project,
            execution_id: str,
            params: Optional[Dict[str, Any]] = None,
            environment_vars: Optional[Dict[str, str]] = None,
            timeout: int = 3600
    ) -> Dict[str, Any]:
        """执行任务

        Args:
            project: 项目对象
            execution_id: 执行ID
            params: 执行参数
            environment_vars: 环境变量
            timeout: 超时时间（秒）

        Returns:
            执行结果
        """
        try:
            # 根据项目类型执行不同的逻辑
            if project.type == ProjectType.FILE:
                return await self._execute_file_project(
                    project, execution_id, params, environment_vars, timeout
                )
            elif project.type == ProjectType.CODE:
                return await self._execute_code_project(
                    project, execution_id, params, environment_vars, timeout
                )
            elif project.type == ProjectType.RULE:
                return await self._execute_rule_project(
                    project, execution_id, params, environment_vars, timeout
                )
            else:
                raise ValueError(f"不支持的项目类型: {project.type}")

        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": "任务执行超时",
                "timeout": timeout
            }
        except Exception as e:
            logger.error(f"执行任务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_rule_project(
            self,
            project: Project,
            execution_id: str,
            params: Optional[Dict[str, Any]] = None,
            environment_vars: Optional[Dict[str, str]] = None,
            timeout: int = 3600
    ) -> Dict[str, Any]:
        """执行规则项目 - 提交任务到Redis"""
        try:
            # 获取规则详情
            rule_detail = await project.rule_detail
            if not rule_detail:
                return {
                    "success": False,
                    "error": "规则项目详情不存在"
                }

            # 连接Redis
            await redis_task_service.connect()

            # 提交任务
            result = await redis_task_service.submit_rule_task(
                project=project,
                rule_detail=rule_detail,
                execution_id=execution_id,
                params=params
            )

            # 断开Redis连接
            await redis_task_service.disconnect()

            return result

        except Exception as e:
            logger.error(f"执行规则项目失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    # ... 其他 _execute_file_project 和 _execute_code_project 方法保持不变
