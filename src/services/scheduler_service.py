# src/services/scheduler_service.py (更新版本)
"""任务调度服务"""
import asyncio
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
from loguru import logger

from src.models.scheduler import ScheduledTask, TaskExecution, TaskLog
from src.models.enums import TaskStatus, TaskType, ScheduleType, ProjectType
from src.services.task_executor import TaskExecutor
from src.services.redis_task_service import redis_task_service  # 新增
from src.services.task_log_service import task_log_service  # 新增
from src.core.config import settings


class SchedulerService:
    """调度器服务"""

    def __init__(self):
        self.scheduler = AsyncIOScheduler(
            timezone=settings.SCHEDULER_TIMEZONE,
            job_defaults={
                'coalesce': settings.TASK_COALESCE,
                'max_instances': settings.TASK_MAX_INSTANCES_PER_TASK,
                'misfire_grace_time': settings.TASK_MISFIRE_GRACE_TIME
            }
        )
        self.executor = TaskExecutor()
        self.running_tasks: Dict[str, Any] = {}

    async def start(self):
        """启动调度器"""
        try:
            self.scheduler.start()
            logger.info("✅ 任务调度器已启动")

            # 加载已存在的活跃任务
            await self._load_active_tasks()

        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise

    async def shutdown(self):
        """关闭调度器"""
        try:
            self.scheduler.shutdown(wait=True)
            logger.info("任务调度器已关闭")
        except Exception as e:
            logger.error(f"关闭调度器失败: {e}")

    async def _load_active_tasks(self):
        """加载活跃任务"""
        try:
            active_tasks = await ScheduledTask.filter(is_active=True).all()
            for task in active_tasks:
                await self.add_task(task)
                logger.info(f"加载任务: {task.name}")
        except Exception as e:
            logger.error(f"加载活跃任务失败: {e}")

    async def add_task(self, task: ScheduledTask):
        """添加任务到调度器"""
        try:
            # 创建触发器
            trigger = self._create_trigger(task)

            # 添加作业
            self.scheduler.add_job(
                func=self._execute_task,
                trigger=trigger,
                id=str(task.id),
                name=task.name,
                kwargs={'task_id': task.id},
                replace_existing=True
            )

            logger.info(f"✅ 任务 {task.name} 已添加到调度器")

        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            raise

    async def remove_task(self, task_id: int):
        """从调度器移除任务"""
        try:
            self.scheduler.remove_job(str(task_id))
            logger.info(f"任务 {task_id} 已从调度器移除")
        except Exception as e:
            logger.error(f"移除任务失败: {e}")
            raise

    async def pause_task(self, task_id: int):
        """暂停任务"""
        try:
            self.scheduler.pause_job(str(task_id))

            # 更新数据库状态
            task = await ScheduledTask.get(id=task_id)
            task.status = TaskStatus.PAUSED
            task.is_active = False
            await task.save()

            logger.info(f"任务 {task_id} 已暂停")
        except Exception as e:
            logger.error(f"暂停任务失败: {e}")
            raise

    async def resume_task(self, task_id: int):
        """恢复任务"""
        try:
            self.scheduler.resume_job(str(task_id))

            # 更新数据库状态
            task = await ScheduledTask.get(id=task_id)
            task.status = TaskStatus.PENDING
            task.is_active = True
            await task.save()

            logger.info(f"任务 {task_id} 已恢复")
        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            raise

    async def trigger_task(self, task_id: int):
        """立即触发任务"""
        try:
            self.scheduler.modify_job(str(task_id))
            self.scheduler.get_job(str(task_id)).modify(next_run_time=datetime.now())
            logger.info(f"任务 {task_id} 已触发")
        except Exception as e:
            logger.error(f"触发任务失败: {e}")
            raise

    def _create_trigger(self, task: ScheduledTask):
        """创建触发器"""
        if task.schedule_type == ScheduleType.CRON:
            return CronTrigger.from_crontab(task.cron_expression)
        elif task.schedule_type == ScheduleType.INTERVAL:
            return IntervalTrigger(seconds=task.interval_seconds)
        elif task.schedule_type == ScheduleType.DATE:
            return DateTrigger(run_date=task.scheduled_time)
        elif task.schedule_type == ScheduleType.ONCE:
            return DateTrigger(run_date=task.scheduled_time or datetime.now())
        else:
            raise ValueError(f"不支持的调度类型: {task.schedule_type}")

    async def _execute_task(self, task_id: int):
        """执行任务的核心方法"""
        execution_id = str(uuid.uuid4())
        task = None
        execution = None

        try:
            # 获取任务
            task = await ScheduledTask.get(id=task_id).prefetch_related('project')

            # 检查任务是否可以执行
            if not task.is_active:
                logger.warning(f"任务 {task.name} 未激活，跳过执行")
                return

            # 生成日志文件路径
            log_paths = task_log_service.generate_log_paths(execution_id, task.name)

            # 创建执行记录
            now = datetime.now(timezone.utc)
            execution = await TaskExecution.create(
                execution_id=execution_id,
                task=task,
                status=TaskStatus.RUNNING,
                start_time=now,
                log_file_path=log_paths["log_file_path"],
                error_log_path=log_paths["error_log_path"]
            )

            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.last_run_time = now
            await task.save()

            # 记录到运行中任务
            self.running_tasks[execution_id] = {
                'task_id': task_id,
                'task_name': task.name,
                'start_time': now
            }

            # 推送开始状态到WebSocket
            await self._push_execution_status(execution, {
                "status": "RUNNING",
                "message": "任务开始执行",
                "task_name": task.name,
                "start_time": now.isoformat()
            })

            # 记录日志
            await self._log_execution(
                execution,
                "INFO",
                f"开始执行任务: {task.name}"
            )

            # 根据项目类型执行不同的逻辑
            if task.project.type == ProjectType.RULE:
                # 规则项目：提交到Redis
                result = await self._execute_rule_task(task, execution)
            else:
                # 文件/代码项目：本地执行
                result = await self.executor.execute(
                    project=task.project,
                    execution_id=execution_id,
                    params=task.execution_params,
                    environment_vars=task.environment_vars,
                    timeout=task.timeout or settings.TASK_EXECUTION_TIMEOUT
                )

            # 处理执行结果
            if result.get('success'):
                execution.status = TaskStatus.SUCCESS
                execution.result = result
                task.status = TaskStatus.SUCCESS
                task.success_count += 1

                await self._log_execution(
                    execution,
                    "INFO",
                    f"任务执行成功: {result.get('message', '执行完成')}"
                )

                # 推送成功状态到WebSocket
                await self._push_execution_status(execution, {
                    "status": "SUCCESS",
                    "message": "任务执行成功",
                    "result": result
                })
            else:
                execution.status = TaskStatus.FAILED
                execution.error_message = result.get('error')
                task.status = TaskStatus.FAILED
                task.failure_count += 1

                await self._log_execution(
                    execution,
                    "ERROR",
                    f"任务执行失败: {result.get('error')}"
                )

                # 推送失败状态到WebSocket
                await self._push_execution_status(execution, {
                    "status": "FAILED",
                    "message": "任务执行失败",
                    "error": result.get('error')
                })

                # 检查是否需要重试
                if task.retry_count > 0 and execution.retry_count < task.retry_count:
                    await self._schedule_retry(task, execution)

        except asyncio.TimeoutError:
            if execution:
                execution.status = TaskStatus.TIMEOUT
                execution.error_message = "任务执行超时"
            if task:
                task.status = TaskStatus.TIMEOUT
                task.failure_count += 1

            await self._log_execution(
                execution,
                "ERROR",
                "任务执行超时"
            )

        except Exception as e:
            logger.error(f"执行任务失败: {e}")
            if execution:
                execution.status = TaskStatus.FAILED
                execution.error_message = str(e)
            if task:
                task.status = TaskStatus.FAILED
                task.failure_count += 1

            await self._log_execution(
                execution,
                "ERROR",
                f"任务执行异常: {str(e)}"
            )

        finally:
            # 清理运行中任务
            if execution_id in self.running_tasks:
                del self.running_tasks[execution_id]

            # 更新执行记录
            if execution:
                execution.end_time = datetime.now(timezone.utc)
                if execution.start_time:
                    execution.duration_seconds = (
                            execution.end_time - execution.start_time
                    ).total_seconds()
                await execution.save()

            # 更新任务状态
            if task:
                if task.status == TaskStatus.RUNNING:
                    task.status = TaskStatus.PENDING
                task.next_run_time = self._get_next_run_time(task_id)
                await task.save()

    async def _execute_rule_task(
            self,
            task: ScheduledTask,
            execution: TaskExecution
    ) -> Dict[str, Any]:
        """执行规则任务 - 提交到Redis"""
        try:
            # 获取项目的规则详情
            project = task.project
            rule_detail = await project.rule_detail

            if not rule_detail:
                return {
                    "success": False,
                    "error": "规则项目详情不存在"
                }

            # 连接Redis
            await redis_task_service.connect()

            # 准备参数
            params = task.execution_params or {}
            params["scheduled_task_id"] = task.id
            params["scheduled_task_name"] = task.name

            # 根据规则配置决定提交策略
            if rule_detail.pagination_config and \
                    rule_detail.pagination_config.get("method") == "url_pattern":
                # URL分页模式：可能需要提交多个任务
                tasks_submitted = []
                start_page = rule_detail.pagination_config.get("start_page", 1)
                max_pages = rule_detail.pagination_config.get("max_pages", 10)

                for page in range(start_page, start_page + max_pages):
                    page_params = params.copy()
                    page_params["page_number"] = page

                    # 替换URL中的页码
                    original_url = rule_detail.target_url
                    if "{}" in original_url:
                        rule_detail.target_url = original_url.format(page)

                    result = await redis_task_service.submit_rule_task(
                        project=project,
                        rule_detail=rule_detail,
                        execution_id=f"{execution.execution_id}_page_{page}",
                        params=page_params
                    )

                    rule_detail.target_url = original_url  # 恢复原URL

                    if result["success"]:
                        tasks_submitted.append(result["task_id"])

                    await self._log_execution(
                        execution,
                        "INFO",
                        f"提交页面 {page} 到Redis: {result.get('task_id')}"
                    )

                # 断开Redis
                await redis_task_service.disconnect()

                return {
                    "success": True,
                    "message": f"成功提交 {len(tasks_submitted)} 个任务到Redis",
                    "task_ids": tasks_submitted,
                    "total_pages": len(tasks_submitted)
                }
            else:
                # 单任务提交
                result = await redis_task_service.submit_rule_task(
                    project=project,
                    rule_detail=rule_detail,
                    execution_id=execution.execution_id,
                    params=params
                )

                # 断开Redis
                await redis_task_service.disconnect()

                if result["success"]:
                    await self._log_execution(
                        execution,
                        "INFO",
                        f"任务已提交到Redis: {result.get('task_id')}"
                    )

                    return {
                        "success": True,
                        "message": f"任务已提交到Redis队列",
                        "task_id": result.get("task_id"),
                        "queue": result.get("queue")
                    }
                else:
                    return {
                        "success": False,
                        "error": result.get("message", "提交失败")
                    }

        except Exception as e:
            logger.error(f"执行规则任务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _schedule_retry(self, task: ScheduledTask, execution: TaskExecution):
        """调度重试"""
        execution.retry_count += 1
        await execution.save()

        # 延迟后重试
        retry_delay = task.retry_delay or settings.TASK_RETRY_DELAY

        self.scheduler.add_job(
            func=self._execute_task,
            trigger=DateTrigger(
                run_date=datetime.now().timestamp() + retry_delay
            ),
            id=f"{task.id}_retry_{execution.retry_count}",
            kwargs={'task_id': task.id}
        )

        await self._log_execution(
            execution,
            "INFO",
            f"任务将在 {retry_delay} 秒后重试 (第{execution.retry_count}次)"
        )

    async def _log_execution(
            self,
            execution: TaskExecution,
            level: str,
            message: str
    ):
        """记录执行日志"""
        if execution and execution.log_file_path:
            # 写入日志文件并实时推送到WebSocket
            log_content = f"[{level}] {message}"
            if level.upper() in ["ERROR", "CRITICAL"]:
                # 错误日志写入错误日志文件
                if execution.error_log_path:
                    await task_log_service.write_log(
                        execution.error_log_path,
                        log_content,
                        execution_id=execution.execution_id
                    )
            else:
                # 普通日志写入输出日志文件
                await task_log_service.write_log(
                    execution.log_file_path,
                    log_content,
                    execution_id=execution.execution_id
                )

            # 同时保留数据库记录（用于快速查询）
            await TaskLog.create(
                execution=execution,
                level=level,
                message=message,
                timestamp=datetime.now(timezone.utc)
            )

    async def _push_execution_status(self, execution: TaskExecution, status_data: Dict[str, Any]):
        """推送执行状态到WebSocket客户端"""
        try:
            from src.services.websocket_log_service import websocket_log_manager

            await websocket_log_manager.send_execution_status(
                execution.execution_id,
                status_data
            )
        except Exception as e:
            logger.error(f"推送执行状态到WebSocket失败: {e}")

    def _get_next_run_time(self, task_id: int) -> Optional[datetime]:
        """获取下次运行时间"""
        job = self.scheduler.get_job(str(task_id))
        if job and job.next_run_time:
            return job.next_run_time
        return None

    def get_running_tasks(self) -> List[Dict[str, Any]]:
        """获取运行中的任务"""
        return list(self.running_tasks.values())


# 创建全局调度器服务实例
scheduler_service = SchedulerService()
