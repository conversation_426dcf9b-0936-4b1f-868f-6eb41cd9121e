"""
WebSocket实时日志服务

负责管理WebSocket连接和实时推送任务执行日志
"""

import json
import asyncio
from datetime import datetime, timezone
from typing import Dict, Set, List, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger


class WebSocketLogManager:
    """WebSocket日志连接管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接
        # 格式: {execution_id: {websocket_id: websocket}}
        self.connections: Dict[str, Dict[str, WebSocket]] = {}
        # 存储连接的元数据
        # 格式: {websocket_id: {"execution_id": str, "user_id": int, "connected_at": datetime}}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        # 连接计数器
        self._connection_counter = 0
    
    def _generate_connection_id(self) -> str:
        """生成唯一的连接ID"""
        self._connection_counter += 1
        return f"ws_{self._connection_counter}_{datetime.now().timestamp()}"
    
    async def connect(self, websocket: WebSocket, execution_id: str, user_id: int) -> str:
        """
        建立WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
            execution_id: 任务执行ID
            user_id: 用户ID
            
        Returns:
            连接ID
        """
        await websocket.accept()
        
        connection_id = self._generate_connection_id()
        
        # 初始化执行ID的连接字典
        if execution_id not in self.connections:
            self.connections[execution_id] = {}
        
        # 存储连接
        self.connections[execution_id][connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "execution_id": execution_id,
            "user_id": user_id,
            "connected_at": datetime.now(timezone.utc)
        }
        
        logger.info(f"✅ WebSocket连接已建立: {connection_id} (执行ID: {execution_id}, 用户: {user_id})")
        
        # 发送连接成功消息
        await self._send_to_connection(websocket, {
            "type": "connection_established",
            "connection_id": connection_id,
            "execution_id": execution_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        return connection_id
    
    async def disconnect(self, connection_id: str):
        """
        断开WebSocket连接
        
        Args:
            connection_id: 连接ID
        """
        if connection_id not in self.connection_metadata:
            return
        
        metadata = self.connection_metadata[connection_id]
        execution_id = metadata["execution_id"]
        
        # 移除连接
        if execution_id in self.connections and connection_id in self.connections[execution_id]:
            del self.connections[execution_id][connection_id]
            
            # 如果该执行ID没有其他连接，删除整个字典
            if not self.connections[execution_id]:
                del self.connections[execution_id]
        
        # 移除元数据
        del self.connection_metadata[connection_id]
        
        logger.info(f"❌ WebSocket连接已断开: {connection_id} (执行ID: {execution_id})")
    
    async def _send_to_connection(self, websocket: WebSocket, message: Dict[str, Any]):
        """
        向单个WebSocket连接发送消息
        
        Args:
            websocket: WebSocket连接对象
            message: 要发送的消息
        """
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
    
    async def broadcast_log(self, execution_id: str, log_data: Dict[str, Any]):
        """
        向指定执行ID的所有连接广播日志消息
        
        Args:
            execution_id: 任务执行ID
            log_data: 日志数据
        """
        if execution_id not in self.connections:
            return
        
        # 准备消息
        message = {
            "type": "log_message",
            "execution_id": execution_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": log_data
        }
        
        # 向所有连接发送消息
        disconnected_connections = []
        for connection_id, websocket in self.connections[execution_id].items():
            try:
                await self._send_to_connection(websocket, message)
            except Exception as e:
                logger.error(f"向连接 {connection_id} 发送日志失败: {e}")
                disconnected_connections.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected_connections:
            await self.disconnect(connection_id)
    
    async def send_execution_status(self, execution_id: str, status_data: Dict[str, Any]):
        """
        向指定执行ID的所有连接发送执行状态更新
        
        Args:
            execution_id: 任务执行ID
            status_data: 状态数据
        """
        if execution_id not in self.connections:
            return
        
        message = {
            "type": "execution_status",
            "execution_id": execution_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": status_data
        }
        
        # 向所有连接发送消息
        disconnected_connections = []
        for connection_id, websocket in self.connections[execution_id].items():
            try:
                await self._send_to_connection(websocket, message)
            except Exception as e:
                logger.error(f"向连接 {connection_id} 发送状态失败: {e}")
                disconnected_connections.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected_connections:
            await self.disconnect(connection_id)
    
    async def send_historical_logs(self, websocket: WebSocket, execution_id: str, limit: int = 100):
        """
        发送历史日志给新连接的客户端
        
        Args:
            websocket: WebSocket连接对象
            execution_id: 任务执行ID
            limit: 日志条数限制
        """
        try:
            from src.services.task_log_service import task_log_service
            
            # 获取历史日志
            logs = await task_log_service.get_execution_logs(execution_id)
            
            if logs["output"]:
                # 解析日志内容并发送
                log_lines = logs["output"].strip().split('\n')
                for line in log_lines[-limit:]:  # 只发送最近的日志
                    if line.strip():
                        await self._send_to_connection(websocket, {
                            "type": "historical_log",
                            "execution_id": execution_id,
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "data": {
                                "level": "INFO",  # 从日志行中解析级别
                                "message": line,
                                "source": "file"
                            }
                        })
            
        except Exception as e:
            logger.error(f"发送历史日志失败: {e}")
            await self._send_to_connection(websocket, {
                "type": "error",
                "message": f"获取历史日志失败: {e}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """
        获取连接统计信息
        
        Returns:
            连接统计数据
        """
        total_connections = len(self.connection_metadata)
        executions_with_connections = len(self.connections)
        
        execution_stats = {}
        for execution_id, connections in self.connections.items():
            execution_stats[execution_id] = len(connections)
        
        return {
            "total_connections": total_connections,
            "executions_with_connections": executions_with_connections,
            "execution_stats": execution_stats,
            "connection_details": [
                {
                    "connection_id": conn_id,
                    "execution_id": metadata["execution_id"],
                    "user_id": metadata["user_id"],
                    "connected_at": metadata["connected_at"].isoformat()
                }
                for conn_id, metadata in self.connection_metadata.items()
            ]
        }


# 创建全局WebSocket日志管理器实例
websocket_log_manager = WebSocketLogManager()
