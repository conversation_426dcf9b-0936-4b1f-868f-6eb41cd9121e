"""
项目服务层
处理项目相关的业务逻辑
"""

import hashlib
from typing import List, Optional, Dict, Any, Tuple
from fastapi import HTTPException, status, UploadFile
from tortoise.exceptions import IntegrityError

from src.models import Project, ProjectFile, ProjectRule, ProjectCode, ProjectType
from src.schemas.project import (
    ProjectCreateRequest, ProjectFileCreateRequest,
    ProjectRuleCreateRequest, ProjectCodeCreateRequest,
    ProjectUpdateRequest, TaskJsonRequest, ProjectRuleUpdateRequest,
    TaskMeta, ExtractionRule, PaginationConfig
)
from .file_storage import file_storage_service


class ProjectService:
    """项目服务类"""
    
    async def create_project(
        self, 
        request: ProjectCreateRequest, 
        user_id: int,
        file: Optional[UploadFile] = None,
        code_file: Optional[UploadFile] = None
    ) -> Project:
        """
        创建项目
        
        Args:
            request: 项目创建请求
            user_id: 用户ID
            file: 文件项目的文件（可选）
            code_file: 代码项目的代码文件（可选）
            
        Returns:
            Project: 创建的项目对象
        """
        try:
            # 创建项目主记录
            project = await Project.create(
                name=request.name,
                description=request.description,
                type=request.type,
                tags=request.tags or [],
                dependencies=request.dependencies,
                user_id=user_id,
                updated_by=user_id
            )
            
            # 根据项目类型创建详情记录
            if request.type == ProjectType.FILE:
                await self._create_file_project_detail(project, request, file)
            elif request.type == ProjectType.RULE:
                await self._create_rule_project_detail(project, request)
            elif request.type == ProjectType.CODE:
                await self._create_code_project_detail(project, request, code_file)
            
            return project
            
        except IntegrityError as e:
            if "name" in str(e):
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="项目名称已存在"
                )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="数据完整性错误"
            )
    
    async def _create_file_project_detail(
        self,
        project: Project,
        request: ProjectFileCreateRequest,
        file: Optional[UploadFile]
    ):
        """创建文件项目详情"""
        if not file:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件项目必须上传文件"
            )

        # 保存文件
        storage_path, file_hash, file_size, file_type = await file_storage_service.save_file(file)

        # 创建文件项目详情
        await ProjectFile.create(
            project=project,
            file_path=storage_path,
            original_name=file.filename,
            file_size=file_size,
            file_type=file_type,
            file_hash=file_hash,
            entry_point=request.entry_point,
            runtime_config=request.runtime_config,
            environment_vars=request.environment_vars,
            storage_type="local",
            is_compressed=file.filename.endswith(('.zip', '.tar.gz'))
        )
    
    async def _create_rule_project_detail(self, project: Project, request: ProjectRuleCreateRequest):
        """创建规则项目详情"""
        
        await ProjectRule.create(
            project=project,
            engine=request.engine,
            target_url=request.target_url,
            url_pattern=request.url_pattern,
            list_selectors=request.list_selectors,
            detail_selectors=request.detail_selectors,
            pagination_type=request.pagination_type,
            pagination_rule=request.pagination_rule,
            max_pages=request.max_pages,
            request_delay=request.request_delay
        )
    
    async def _create_code_project_detail(
        self,
        project: Project,
        request: ProjectCodeCreateRequest,
        code_file: Optional[UploadFile]
    ):
        """创建代码项目详情"""
        if not code_file:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="代码项目必须上传代码文件"
            )

        # 读取代码内容
        try:
            code_content = (await code_file.read()).decode('utf-8')
        except UnicodeDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="代码文件必须是UTF-8编码的文本文件"
            )

        # 计算内容哈希
        content_hash = hashlib.md5(code_content.encode('utf-8')).hexdigest()

        # 创建代码项目详情
        await ProjectCode.create(
            project=project,
            content=code_content,
            language=request.language,
            version=request.version,
            content_hash=content_hash,
            entry_point=request.entry_point,
            documentation=request.documentation
        )
    
    async def get_project_by_id(self, project_id: int, user_id: Optional[int] = None) -> Optional[Project]:
        """根据ID获取项目"""
        query = Project.filter(id=project_id)
        
        # 如果指定了用户ID，则只返回该用户的项目
        if user_id is not None:
            query = query.filter(user_id=user_id)
        
        project = await query.first()
        if not project:
            return None
        
        # 预加载关联数据
        if project.type == ProjectType.FILE:
            await project.fetch_related('file_detail')
        elif project.type == ProjectType.RULE:
            await project.fetch_related('rule_detail')
        elif project.type == ProjectType.CODE:
            await project.fetch_related('code_detail')
        
        return project
    
    async def get_projects_list(
        self, 
        page: int = 1, 
        size: int = 20,
        project_type: Optional[ProjectType] = None,
        status: Optional[str] = None,
        tag: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> Tuple[List[Project], int]:
        """获取项目列表"""
        query = Project.all()
        
        # 应用筛选条件
        if project_type:
            query = query.filter(type=project_type)
        if status:
            query = query.filter(status=status)
        if tag:
            query = query.filter(tags__contains=tag)
        if user_id:
            query = query.filter(user_id=user_id)
        
        # 获取总数
        total = await query.count()
        
        # 分页查询
        offset = (page - 1) * size
        projects = await query.offset(offset).limit(size).order_by('-created_at')
        
        return projects, total
    
    async def update_project(
        self, 
        project_id: int, 
        request: ProjectUpdateRequest, 
        user_id: int
    ) -> Optional[Project]:
        """更新项目"""
        project = await Project.filter(id=project_id, user_id=user_id).first()
        if not project:
            return None
        
        # 更新字段
        update_data = request.dict(exclude_unset=True)
        if update_data:
            update_data['updated_by'] = user_id
            await project.update_from_dict(update_data)
            await project.save()
        
        return project
    
    async def delete_project(self, project_id: int, user_id: int) -> bool:
        """删除项目"""
        project = await Project.filter(id=project_id, user_id=user_id).first()
        if not project:
            return False
        
        # 如果是文件项目，删除关联的文件
        if project.type == ProjectType.FILE:
            file_detail = await ProjectFile.filter(project=project).first()
            if file_detail:
                file_storage_service.delete_file(file_detail.file_path)
        
        # 删除项目（级联删除详情记录）
        await project.delete()
        return True

    async def generate_task_json(self, rule_detail: ProjectRule) -> TaskJsonRequest:
        """根据规则项目配置生成任务JSON"""
        import random
        import string
        from datetime import datetime

        # 构建提取规则
        rules = []
        if rule_detail.extraction_rules:
            # 使用新的extraction_rules格式
            for rule_data in rule_detail.extraction_rules:
                rules.append(ExtractionRule(**rule_data))
        else:
            # 兼容旧版本的list_selectors和detail_selectors
            if rule_detail.list_selectors:
                for selector in rule_detail.list_selectors:
                    rules.append(ExtractionRule(
                        desc=selector.get('desc', '列表页选择器'),
                        type=selector.get('type', 'css'),
                        expr=selector.get('expr', '')
                    ))
            if rule_detail.detail_selectors:
                for selector in rule_detail.detail_selectors:
                    rules.append(ExtractionRule(
                        desc=selector.get('desc', '详情页选择器'),
                        type=selector.get('type', 'css'),
                        expr=selector.get('expr', '')
                    ))

        # 构建分页配置
        pagination_config = None
        if rule_detail.pagination_config:
            pagination_config = PaginationConfig(**rule_detail.pagination_config)
        elif rule_detail.pagination_type:
            # 兼容旧版本
            pagination_config = PaginationConfig(
                method=rule_detail.pagination_type,
                start_page=rule_detail.start_page,
                max_pages=rule_detail.max_pages
            )

        # 生成任务ID
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3]  # 精确到毫秒
        random_hex = ''.join(random.choices(string.hexdigits.lower(), k=7))
        task_id = f"crawler-{timestamp}-{random_hex}"

        # 构建任务元数据
        task_meta = TaskMeta(
            fetch_type=rule_detail.engine,
            pagination=pagination_config,
            rules=rules,
            page_number=1 if pagination_config else None,
            proxy=rule_detail.proxy_config.get('proxy') if rule_detail.proxy_config else None,
            task_id=task_id,
            worker_id=rule_detail.task_config.get('worker_id', 'Scraper-Node-Default-01') if rule_detail.task_config else 'Scraper-Node-Default-01'
        )

        # 构建任务JSON
        task_json = TaskJsonRequest(
            url=rule_detail.target_url,
            callback=rule_detail.callback_type,
            method=rule_detail.request_method,
            meta=task_meta,
            headers=rule_detail.headers,
            cookies=rule_detail.cookies,
            priority=rule_detail.priority,
            dont_filter=rule_detail.dont_filter
        )

        return task_json

    async def update_rule_config(
        self,
        project_id: int,
        request: ProjectRuleUpdateRequest,
        user_id: int
    ) -> Project:
        """更新规则项目配置"""

        # 获取项目
        project = await Project.filter(id=project_id, user_id=user_id).first()
        if not project:
            return None

        # 获取规则详情
        rule_detail = await ProjectRule.filter(project=project).first()
        if not rule_detail:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="规则项目配置不存在"
            )

        # 更新规则配置
        update_data = {}
        if request.target_url is not None:
            update_data['target_url'] = request.target_url
        if request.callback_type is not None:
            update_data['callback_type'] = request.callback_type
        if request.request_method is not None:
            update_data['request_method'] = request.request_method
        if request.extraction_rules is not None:
            update_data['extraction_rules'] = [rule.dict() for rule in request.extraction_rules]
        if request.pagination_config is not None:
            update_data['pagination_config'] = request.pagination_config.dict()
        if request.max_pages is not None:
            update_data['max_pages'] = request.max_pages
        if request.start_page is not None:
            update_data['start_page'] = request.start_page
        if request.request_delay is not None:
            update_data['request_delay'] = request.request_delay
        if request.priority is not None:
            update_data['priority'] = request.priority
        if request.dont_filter is not None:
            update_data['dont_filter'] = request.dont_filter
        if request.headers is not None:
            update_data['headers'] = request.headers
        if request.cookies is not None:
            update_data['cookies'] = request.cookies
        if request.proxy_config is not None:
            update_data['proxy_config'] = {'proxy': request.proxy_config}
        if request.task_config is not None:
            update_data['task_config'] = request.task_config

        # 应用更新
        if update_data:
            await rule_detail.update_from_dict(update_data)
            await rule_detail.save()

            # 更新项目的更新时间
            project.updated_by = user_id
            await project.save()

        return project


# 创建项目服务实例
project_service = ProjectService()
