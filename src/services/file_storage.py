"""
文件存储服务
处理文件上传、存储、验证等功能
"""

import os
import hashlib
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple, BinaryIO
from fastapi import UploadFile, HTTPException, status

from src.core.config import settings


class FileStorageService:
    """文件存储服务类"""
    
    # 支持的文件类型
    ALLOWED_EXTENSIONS = {
        '.zip', '.tar.gz', '.py', '.txt', '.json', '.md', '.yml', '.yaml'
    }
    
    # 最大文件大小 (100MB)
    MAX_FILE_SIZE = 100 * 1024 * 1024
    
    def __init__(self):
        self.storage_root = getattr(settings, 'LOCAL_STORAGE_PATH', './storage/projects')
        self._ensure_storage_directory()
    
    def _ensure_storage_directory(self):
        """确保存储目录存在"""
        Path(self.storage_root).mkdir(parents=True, exist_ok=True)
    
    def _get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        if filename.endswith('.tar.gz'):
            return '.tar.gz'
        return Path(filename).suffix.lower()
    
    def _validate_file_type(self, filename: str) -> bool:
        """验证文件类型"""
        extension = self._get_file_extension(filename)
        return extension in self.ALLOWED_EXTENSIONS
    
    def _validate_file_size(self, file_size: int) -> bool:
        """验证文件大小"""
        return file_size <= self.MAX_FILE_SIZE
    
    def _calculate_md5(self, file_content: bytes) -> str:
        """计算文件MD5哈希"""
        return hashlib.md5(file_content).hexdigest()
    
    def _generate_storage_path(self, extension: str) -> str:
        """生成存储路径"""
        now = datetime.now()
        year = now.strftime('%Y')
        month = now.strftime('%m')
        day = now.strftime('%d')
        
        # 生成UUID文件名
        file_uuid = str(uuid.uuid4())
        filename = f"{file_uuid}{extension}"
        
        # 构建完整路径
        relative_path = f"{year}/{month}/{day}/{filename}"
        full_path = os.path.join(self.storage_root, relative_path)
        
        # 确保目录存在
        Path(full_path).parent.mkdir(parents=True, exist_ok=True)
        
        return relative_path
    
    async def save_file(self, file: UploadFile) -> Tuple[str, str, int, str]:
        """
        保存上传的文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            Tuple[str, str, int, str]: (存储路径, 文件哈希, 文件大小, 文件类型)
            
        Raises:
            HTTPException: 文件验证失败时抛出异常
        """
        # 验证文件名
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名不能为空"
            )
        
        # 验证文件类型
        if not self._validate_file_type(file.filename):
            raise HTTPException(
                status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                detail=f"不支持的文件类型。支持的类型: {', '.join(self.ALLOWED_EXTENSIONS)}"
            )
        
        # 读取文件内容
        try:
            file_content = await file.read()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"读取文件失败: {str(e)}"
            )
        
        # 验证文件大小
        file_size = len(file_content)
        if not self._validate_file_size(file_size):
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"文件大小超出限制。最大允许: {self.MAX_FILE_SIZE / 1024 / 1024:.1f}MB"
            )
        
        # 计算文件哈希
        file_hash = self._calculate_md5(file_content)
        
        # 获取文件扩展名和类型
        extension = self._get_file_extension(file.filename)
        file_type = file.content_type or 'application/octet-stream'
        
        # 生成存储路径
        storage_path = self._generate_storage_path(extension)
        full_path = os.path.join(self.storage_root, storage_path)
        
        # 保存文件
        try:
            with open(full_path, 'wb') as f:
                f.write(file_content)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件保存失败: {str(e)}"
            )
        
        return storage_path, file_hash, file_size, file_type
    
    def delete_file(self, storage_path: str) -> bool:
        """
        删除存储的文件
        
        Args:
            storage_path: 文件存储路径
            
        Returns:
            bool: 删除是否成功
        """
        try:
            full_path = os.path.join(self.storage_root, storage_path)
            if os.path.exists(full_path):
                os.remove(full_path)
                return True
            return False
        except Exception:
            return False
    
    def get_file_path(self, storage_path: str) -> str:
        """
        获取文件的完整路径
        
        Args:
            storage_path: 文件存储路径
            
        Returns:
            str: 文件的完整路径
        """
        return os.path.join(self.storage_root, storage_path)
    
    def file_exists(self, storage_path: str) -> bool:
        """
        检查文件是否存在
        
        Args:
            storage_path: 文件存储路径
            
        Returns:
            bool: 文件是否存在
        """
        full_path = os.path.join(self.storage_root, storage_path)
        return os.path.exists(full_path)


# 创建文件存储服务实例
file_storage_service = FileStorageService()
