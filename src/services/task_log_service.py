"""
任务日志管理服务

负责任务执行日志的创建、写入、读取和管理
"""

import os
import asyncio
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional, Dict, Any, List
from loguru import logger

from src.core.config import settings


class TaskLogService:
    """任务日志管理服务"""
    
    def __init__(self):
        self.log_dir = Path(settings.TASK_LOG_DIR)
        self.max_log_size = settings.TASK_LOG_MAX_SIZE
        self._ensure_log_directory()
    
    def _ensure_log_directory(self):
        """确保日志目录存在"""
        try:
            self.log_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ 任务日志目录已准备: {self.log_dir}")
        except Exception as e:
            logger.error(f"❌ 创建任务日志目录失败: {e}")
            raise
    
    def generate_log_paths(self, execution_id: str, task_name: str) -> Dict[str, str]:
        """
        生成日志文件路径
        
        Args:
            execution_id: 执行ID
            task_name: 任务名称
            
        Returns:
            包含日志文件路径的字典
        """
        # 使用日期和执行ID创建目录结构
        date_str = datetime.now().strftime("%Y-%m-%d")
        task_log_dir = self.log_dir / date_str / execution_id
        
        # 确保目录存在
        task_log_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成日志文件路径
        log_file_path = task_log_dir / "output.log"
        error_log_path = task_log_dir / "error.log"
        
        return {
            "log_file_path": str(log_file_path),
            "error_log_path": str(error_log_path),
            "log_dir": str(task_log_dir)
        }
    
    async def write_log(self, log_file_path: str, content: str, append: bool = True, execution_id: Optional[str] = None):
        """
        写入日志内容到文件并实时推送到WebSocket

        Args:
            log_file_path: 日志文件路径
            content: 日志内容
            append: 是否追加模式
            execution_id: 任务执行ID，用于WebSocket推送
        """
        try:
            mode = "a" if append else "w"

            # 检查文件大小
            if os.path.exists(log_file_path) and os.path.getsize(log_file_path) > self.max_log_size:
                logger.warning(f"日志文件 {log_file_path} 超过最大大小限制")
                return

            # 异步写入日志
            def write_sync():
                with open(log_file_path, mode, encoding='utf-8') as f:
                    timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
                    formatted_content = f"[{timestamp}] {content}\n"
                    f.write(formatted_content)
                    return formatted_content

            formatted_content = await asyncio.get_event_loop().run_in_executor(None, write_sync)

            # 实时推送到WebSocket客户端
            if execution_id:
                await self._push_to_websocket(execution_id, content, log_file_path)

        except Exception as e:
            logger.error(f"写入日志文件失败 {log_file_path}: {e}")

    async def _push_to_websocket(self, execution_id: str, content: str, log_file_path: str):
        """
        推送日志到WebSocket客户端

        Args:
            execution_id: 任务执行ID
            content: 日志内容
            log_file_path: 日志文件路径
        """
        try:
            # 延迟导入避免循环依赖
            from src.services.websocket_log_service import websocket_log_manager

            # 解析日志级别
            level = "INFO"
            if "[ERROR]" in content:
                level = "ERROR"
            elif "[WARNING]" in content:
                level = "WARNING"
            elif "[DEBUG]" in content:
                level = "DEBUG"

            # 确定日志类型
            log_type = "error" if "error.log" in log_file_path else "output"

            # 推送到WebSocket
            await websocket_log_manager.broadcast_log(execution_id, {
                "level": level,
                "message": content,
                "log_type": log_type,
                "file_path": log_file_path,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

        except Exception as e:
            logger.error(f"推送日志到WebSocket失败: {e}")
    
    async def read_log(self, log_file_path: str, lines: Optional[int] = None) -> str:
        """
        读取日志文件内容
        
        Args:
            log_file_path: 日志文件路径
            lines: 读取的行数，None表示读取全部
            
        Returns:
            日志内容
        """
        try:
            if not os.path.exists(log_file_path):
                return ""
            
            def read_sync():
                with open(log_file_path, 'r', encoding='utf-8') as f:
                    if lines is None:
                        return f.read()
                    else:
                        # 读取最后N行
                        all_lines = f.readlines()
                        return ''.join(all_lines[-lines:] if len(all_lines) > lines else all_lines)
            
            content = await asyncio.get_event_loop().run_in_executor(None, read_sync)
            return content
            
        except Exception as e:
            logger.error(f"读取日志文件失败 {log_file_path}: {e}")
            return f"读取日志失败: {e}"
    
    async def get_log_info(self, log_file_path: str) -> Dict[str, Any]:
        """
        获取日志文件信息
        
        Args:
            log_file_path: 日志文件路径
            
        Returns:
            日志文件信息
        """
        try:
            if not os.path.exists(log_file_path):
                return {
                    "exists": False,
                    "size": 0,
                    "lines": 0,
                    "modified_time": None
                }
            
            def get_info_sync():
                stat = os.stat(log_file_path)
                with open(log_file_path, 'r', encoding='utf-8') as f:
                    lines = sum(1 for _ in f)
                
                return {
                    "exists": True,
                    "size": stat.st_size,
                    "lines": lines,
                    "modified_time": datetime.fromtimestamp(stat.st_mtime, timezone.utc)
                }
            
            return await asyncio.get_event_loop().run_in_executor(None, get_info_sync)
            
        except Exception as e:
            logger.error(f"获取日志文件信息失败 {log_file_path}: {e}")
            return {
                "exists": False,
                "size": 0,
                "lines": 0,
                "modified_time": None,
                "error": str(e)
            }
    
    async def cleanup_old_logs(self, retention_days: int = None):
        """
        清理过期的日志文件
        
        Args:
            retention_days: 保留天数，默认使用配置值
        """
        if retention_days is None:
            retention_days = settings.TASK_LOG_RETENTION_DAYS
        
        try:
            cutoff_time = datetime.now(timezone.utc).timestamp() - (retention_days * 24 * 3600)
            deleted_count = 0
            
            def cleanup_sync():
                nonlocal deleted_count
                for root, dirs, files in os.walk(self.log_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if os.path.getmtime(file_path) < cutoff_time:
                            os.remove(file_path)
                            deleted_count += 1
                
                # 删除空目录
                for root, dirs, files in os.walk(self.log_dir, topdown=False):
                    for dir_name in dirs:
                        dir_path = os.path.join(root, dir_name)
                        try:
                            os.rmdir(dir_path)  # 只删除空目录
                        except OSError:
                            pass  # 目录不为空，跳过
            
            await asyncio.get_event_loop().run_in_executor(None, cleanup_sync)
            logger.info(f"✅ 清理了 {deleted_count} 个过期日志文件")
            
        except Exception as e:
            logger.error(f"清理过期日志失败: {e}")
    
    async def get_execution_logs(self, execution_id: str) -> Dict[str, str]:
        """
        获取指定执行ID的所有日志
        
        Args:
            execution_id: 执行ID
            
        Returns:
            包含输出日志和错误日志的字典
        """
        # 查找日志文件
        log_files = []
        for date_dir in self.log_dir.iterdir():
            if date_dir.is_dir():
                execution_dir = date_dir / execution_id
                if execution_dir.exists():
                    log_files.append(execution_dir)
        
        if not log_files:
            return {"output": "", "error": ""}
        
        # 假设只有一个匹配的目录（最新的）
        execution_dir = log_files[0]
        
        output_log = await self.read_log(str(execution_dir / "output.log"))
        error_log = await self.read_log(str(execution_dir / "error.log"))
        
        return {
            "output": output_log,
            "error": error_log
        }


# 创建全局实例
task_log_service = TaskLogService()
