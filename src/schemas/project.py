"""
项目相关的Pydantic模式定义
包含项目创建、更新、响应等数据模式
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from fastapi import UploadFile, Form, File

from src.models.enums import ProjectType, ProjectStatus, CrawlEngine, PaginationType, RuleType, CallbackType, RequestMethod


class ProjectCreateRequest(BaseModel):
    """项目创建请求基础模式"""
    name: str = Field(..., min_length=3, max_length=50, description="项目名称")
    description: Optional[str] = Field(None, max_length=500, description="项目描述")
    type: ProjectType = Field(..., description="项目类型")
    tags: Optional[Union[str, List[str]]] = Field(None, description="项目标签")
    dependencies: Optional[Union[str, List[str]]] = Field(None, description="Python依赖包")

    @validator('tags', pre=True)
    def parse_tags(cls, v):
        """解析标签字段"""
        if v is None:
            return []
        if isinstance(v, str):
            # 如果是逗号分隔的字符串
            if v.startswith('[') and v.endswith(']'):
                # JSON数组格式
                import json
                try:
                    return json.loads(v)
                except:
                    return []
            else:
                # 逗号分隔格式
                return [tag.strip() for tag in v.split(',') if tag.strip()]
        return v

    @validator('dependencies', pre=True)
    def parse_dependencies(cls, v):
        """解析依赖包字段"""
        if v is None:
            return None
        if isinstance(v, str):
            import json
            try:
                return json.loads(v)
            except:
                return None
        return v


class ProjectFileCreateRequest(ProjectCreateRequest):
    """文件项目创建请求"""
    entry_point: Optional[str] = Field(None, max_length=255, description="入口文件路径")
    runtime_config: Optional[Union[str, Dict[str, Any]]] = Field(None, description="运行时配置")
    environment_vars: Optional[Union[str, Dict[str, Any]]] = Field(None, description="环境变量")

    @validator('runtime_config', pre=True)
    def parse_runtime_config(cls, v):
        """解析运行时配置"""
        if v is None:
            return None
        if isinstance(v, str):
            import json
            try:
                return json.loads(v)
            except:
                return None
        return v

    @validator('environment_vars', pre=True)
    def parse_environment_vars(cls, v):
        """解析环境变量"""
        if v is None:
            return None
        if isinstance(v, str):
            import json
            try:
                return json.loads(v)
            except:
                return None
        return v


class ProjectRuleCreateRequest(ProjectCreateRequest):
    """规则项目创建请求"""
    engine: Optional[CrawlEngine] = Field(CrawlEngine.REQUESTS, description="采集引擎 (browser: 浏览器引擎, requests: HTTP库, curl_cffi: curl模拟)")
    target_url: str = Field(..., max_length=2000, description="目标URL")
    url_pattern: Optional[str] = Field(None, max_length=500, description="URL匹配模式")
    list_selectors: Optional[Union[str, List[Dict[str, Any]]]] = Field(None, description="列表页选择器")
    detail_selectors: Union[str, List[Dict[str, Any]]] = Field(..., description="详情页选择器")
    pagination_type: Optional[PaginationType] = Field(None, description="翻页类型")
    pagination_rule: Optional[str] = Field(None, max_length=500, description="翻页规则")
    max_pages: Optional[int] = Field(10, ge=1, le=1000, description="最大页数")
    request_delay: Optional[int] = Field(1000, ge=0, description="请求间隔(ms)")

    @validator('list_selectors', 'detail_selectors', pre=True)
    def parse_selectors(cls, v):
        """解析选择器配置"""
        if v is None:
            return None
        if isinstance(v, str):
            import json
            try:
                return json.loads(v)
            except:
                return None
        return v


class ProjectCodeCreateRequest(ProjectCreateRequest):
    """代码项目创建请求"""
    language: Optional[str] = Field("python", max_length=50, description="编程语言")
    version: Optional[str] = Field("1.0.0", max_length=20, description="版本号")
    entry_point: Optional[str] = Field(None, max_length=255, description="入口函数")
    documentation: Optional[str] = Field(None, description="代码文档")


class ProjectUpdateRequest(BaseModel):
    """项目更新请求"""
    name: Optional[str] = Field(None, min_length=3, max_length=50, description="项目名称")
    description: Optional[str] = Field(None, max_length=500, description="项目描述")
    status: Optional[ProjectStatus] = Field(None, description="项目状态")
    tags: Optional[List[str]] = Field(None, description="项目标签")
    dependencies: Optional[List[str]] = Field(None, description="Python依赖包")


# Form参数模型（用于multipart/form-data请求）
class ProjectCreateFormRequest(BaseModel):
    """项目创建Form请求模式（用于文件上传）"""
    # 通用参数
    name: str = Field(..., min_length=3, max_length=50, description="项目名称")
    description: Optional[str] = Field(None, max_length=500, description="项目描述")
    type: ProjectType = Field(..., description="项目类型")
    tags: Optional[str] = Field(None, description="项目标签，逗号分隔或JSON数组")
    dependencies: Optional[str] = Field(None, description="Python依赖包JSON数组")

    # 文件项目参数
    entry_point: Optional[str] = Field(None, max_length=255, description="入口文件路径")
    runtime_config: Optional[str] = Field(None, description="运行时配置JSON")
    environment_vars: Optional[str] = Field(None, description="环境变量JSON")

    # 规则项目参数
    engine: Optional[str] = Field("requests", description="采集引擎 (browser/requests/curl_cffi)")
    target_url: Optional[str] = Field(None, max_length=2000, description="目标URL")
    url_pattern: Optional[str] = Field(None, max_length=500, description="URL匹配模式")
    list_selectors: Optional[str] = Field(None, description="列表页选择器JSON")
    detail_selectors: Optional[str] = Field(None, description="详情页选择器JSON")
    pagination_type: Optional[str] = Field(None, description="翻页类型")
    pagination_rule: Optional[str] = Field(None, max_length=500, description="翻页规则")
    max_pages: Optional[int] = Field(10, ge=1, le=1000, description="最大页数")
    request_delay: Optional[int] = Field(1000, ge=0, description="请求间隔(ms)")

    # 代码项目参数
    language: Optional[str] = Field("python", max_length=50, description="编程语言")
    version: Optional[str] = Field("1.0.0", max_length=20, description="版本号")
    code_entry_point: Optional[str] = Field(None, max_length=255, description="入口函数")
    documentation: Optional[str] = Field(None, description="代码文档")


class ProjectListQueryRequest(BaseModel):
    """项目列表查询参数模式"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    type: Optional[ProjectType] = Field(None, description="项目类型筛选")
    status: Optional[ProjectStatus] = Field(None, description="项目状态筛选")
    tag: Optional[str] = Field(None, description="标签筛选")


# 新增：支持任务JSON格式的模型
class ExtractionRule(BaseModel):
    """提取规则模型"""
    desc: str = Field(..., description="规则描述")
    type: RuleType = Field(..., description="规则类型")
    expr: str = Field(..., description="规则表达式")


class PaginationConfig(BaseModel):
    """分页配置模型"""
    method: PaginationType = Field(..., description="分页方法")
    start_page: int = Field(1, description="起始页码")
    max_pages: int = Field(10, description="最大页数")
    next_page_rule: Optional[ExtractionRule] = Field(None, description="下一页规则（点击翻页用）")
    wait_after_click_ms: Optional[int] = Field(None, description="点击后等待时间（毫秒）")


class TaskMeta(BaseModel):
    """任务元数据模型"""
    fetch_type: CrawlEngine = Field(..., description="爬取方式")
    pagination: Optional[PaginationConfig] = Field(None, description="分页配置")
    rules: List[ExtractionRule] = Field(..., description="提取规则列表")
    page_number: Optional[int] = Field(None, description="当前页码")
    proxy: Optional[str] = Field(None, description="代理配置")
    task_id: Optional[str] = Field(None, description="任务ID")
    worker_id: Optional[str] = Field(None, description="工作节点ID")


class TaskJsonRequest(BaseModel):
    """任务JSON请求模型"""
    url: str = Field(..., description="目标URL")
    callback: CallbackType = Field(..., description="回调类型")
    method: RequestMethod = Field(RequestMethod.GET, description="请求方法")
    meta: TaskMeta = Field(..., description="任务元数据")
    headers: Optional[Dict[str, str]] = Field(None, description="请求头")
    cookies: Optional[Dict[str, str]] = Field(None, description="Cookie")
    priority: int = Field(0, description="优先级")
    dont_filter: bool = Field(False, description="是否去重")


class ProjectRuleUpdateRequest(BaseModel):
    """规则项目更新请求模型"""
    target_url: Optional[str] = Field(None, max_length=2000, description="目标URL")
    callback_type: Optional[CallbackType] = Field(None, description="回调类型")
    request_method: Optional[RequestMethod] = Field(None, description="请求方法")
    extraction_rules: Optional[List[ExtractionRule]] = Field(None, description="提取规则数组")
    pagination_config: Optional[PaginationConfig] = Field(None, description="分页配置")
    max_pages: Optional[int] = Field(None, ge=1, le=1000, description="最大页数")
    start_page: Optional[int] = Field(None, ge=1, description="起始页码")
    request_delay: Optional[int] = Field(None, ge=0, description="请求间隔(ms)")
    priority: Optional[int] = Field(None, description="优先级")
    dont_filter: Optional[bool] = Field(None, description="是否去重")
    headers: Optional[Dict[str, str]] = Field(None, description="请求头")
    cookies: Optional[Dict[str, str]] = Field(None, description="Cookie")
    proxy_config: Optional[str] = Field(None, description="代理配置")
    task_config: Optional[Dict[str, Any]] = Field(None, description="任务配置")


class FileInfo(BaseModel):
    """文件信息"""
    original_name: str = Field(description="原始文件名")
    file_size: int = Field(description="文件大小")
    file_hash: str = Field(description="文件哈希")


class ProjectResponse(BaseModel):
    """项目响应模式"""
    id: int = Field(description="项目ID")
    name: str = Field(description="项目名称")
    description: Optional[str] = Field(description="项目描述")
    type: ProjectType = Field(description="项目类型")
    status: ProjectStatus = Field(description="项目状态")
    tags: List[str] = Field(description="项目标签")
    dependencies: Optional[List[str]] = Field(description="Python依赖包")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    created_by: int = Field(description="创建者ID")
    download_count: int = Field(description="下载次数")
    star_count: int = Field(description="收藏次数")
    file_info: Optional[FileInfo] = Field(None, description="文件信息")

    @classmethod
    def from_orm(cls, obj):
        """从ORM对象创建响应对象"""
        data = {}
        for field_name, field_info in cls.model_fields.items():
            if field_name == 'created_by':
                # 映射 user_id 字段到 created_by
                data[field_name] = getattr(obj, 'user_id', None)
            else:
                data[field_name] = getattr(obj, field_name, None)
        return cls(**data)

    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    """项目列表响应模式"""
    id: int = Field(description="项目ID")
    name: str = Field(description="项目名称")
    description: Optional[str] = Field(description="项目描述")
    type: ProjectType = Field(description="项目类型")
    status: ProjectStatus = Field(description="项目状态")
    tags: List[str] = Field(description="项目标签")
    created_at: datetime = Field(description="创建时间")
    created_by: int = Field(description="创建者ID")
    download_count: int = Field(description="下载次数")
    star_count: int = Field(description="收藏次数")

    class Config:
        from_attributes = True
