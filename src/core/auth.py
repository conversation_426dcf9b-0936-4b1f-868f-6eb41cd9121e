"""
JWT认证相关功能
包含token生成、验证、用户身份认证等功能
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import jwt
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

from .config import settings


class TokenData(BaseModel):
    """Token数据模型"""
    user_id: int
    username: str
    exp: datetime


class JWTAuth:
    """JWT认证类"""
    
    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.expire_minutes = settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
        
    def create_access_token(self, user_id: int, username: str, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.expire_minutes)
            
        to_encode = {
            "user_id": user_id,
            "username": username,
            "exp": expire
        }
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> TokenData:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            user_id: int = payload.get("user_id")
            username: str = payload.get("username")
            exp: datetime = datetime.fromtimestamp(payload.get("exp"))
            
            if user_id is None or username is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的认证凭据",
                    headers={"WWW-Authenticate": "Bearer"},
                )
                
            return TokenData(user_id=user_id, username=username, exp=exp)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )


# 创建JWT认证实例
jwt_auth = JWTAuth()

# HTTP Bearer认证方案
security = HTTPBearer()


def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """获取当前用户信息"""
    token = credentials.credentials
    return jwt_auth.verify_token(token)


def get_current_user_id(current_user: TokenData = Depends(get_current_user)) -> int:
    """获取当前用户ID"""
    return current_user.user_id


async def get_current_admin_user(current_user: TokenData = Depends(get_current_user)) -> TokenData:
    """获取当前管理员用户（仅管理员可访问）"""
    from src.models.user import User

    # 从数据库获取用户信息验证管理员权限
    user = await User.get_or_none(id=current_user.user_id)
    if not user or not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )

    return current_user


# 可选的认证依赖（用于测试环境）
def get_optional_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[TokenData]:
    """获取当前用户信息（可选）"""
    if not credentials:
        return None
    
    token = credentials.credentials
    try:
        return jwt_auth.verify_token(token)
    except HTTPException:
        return None


def create_test_token(user_id: int = 1, username: str = "test_user") -> str:
    """创建测试用的JWT令牌（仅用于开发和测试）"""
    return jwt_auth.create_access_token(user_id=user_id, username=username)
