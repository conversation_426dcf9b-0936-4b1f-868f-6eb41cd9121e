"""
自定义异常和错误处理
定义业务异常和全局异常处理器
"""

from typing import Any, Dict, List, Optional
from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
import traceback
from datetime import datetime

from src.schemas.common import ErrorResponse, ErrorDetail


class BusinessException(HTTPException):
    """业务异常基类"""
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        error_code: Optional[str] = None,
        errors: Optional[List[ErrorDetail]] = None
    ):
        super().__init__(status_code=status_code, detail=detail)
        self.error_code = error_code
        self.errors = errors or []


class ProjectNameExistsException(BusinessException):
    """项目名称已存在异常"""
    
    def __init__(self, name: str):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"项目名称 '{name}' 已存在",
            error_code="DUPLICATE_PROJECT_NAME"
        )


class ProjectNotFoundException(BusinessException):
    """项目不存在异常"""
    
    def __init__(self, project_id: int):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"项目 ID {project_id} 不存在",
            error_code="PROJECT_NOT_FOUND"
        )


class InvalidFileTypeException(BusinessException):
    """无效文件类型异常"""
    
    def __init__(self, file_type: str, allowed_types: List[str]):
        super().__init__(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail=f"不支持的文件类型 '{file_type}'。支持的类型: {', '.join(allowed_types)}",
            error_code="INVALID_FILE_TYPE"
        )


class FileTooLargeException(BusinessException):
    """文件过大异常"""
    
    def __init__(self, file_size: int, max_size: int):
        super().__init__(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"文件大小 {file_size} 字节超出限制。最大允许: {max_size} 字节",
            error_code="FILE_TOO_LARGE"
        )


class InvalidURLException(BusinessException):
    """无效URL异常"""
    
    def __init__(self, url: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的URL: {url}",
            error_code="INVALID_URL"
        )


class InvalidSelectorException(BusinessException):
    """无效选择器异常"""
    
    def __init__(self, selector: str, selector_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的{selector_type}选择器: {selector}",
            error_code="INVALID_SELECTOR"
        )


class StorageException(BusinessException):
    """存储异常"""
    
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"存储错误: {detail}",
            error_code="STORAGE_ERROR"
        )


def create_error_response(
    status_code: int,
    message: str,
    error_code: Optional[str] = None,
    errors: Optional[List[ErrorDetail]] = None
) -> JSONResponse:
    """创建错误响应"""
    error_response = ErrorResponse(
        code=status_code,
        message=message,
        errors=errors,
        timestamp=datetime.now()
    )

    # 使用 model_dump 并手动处理 datetime 序列化
    content = error_response.model_dump()
    # 转换 datetime 对象为 ISO 格式字符串
    if 'timestamp' in content and isinstance(content['timestamp'], datetime):
        content['timestamp'] = content['timestamp'].isoformat()

    return JSONResponse(
        status_code=status_code,
        content=content
    )


async def business_exception_handler(request: Request, exc: BusinessException) -> JSONResponse:
    """业务异常处理器"""
    return create_error_response(
        status_code=exc.status_code,
        message=exc.detail,
        error_code=exc.error_code,
        errors=exc.errors
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    return create_error_response(
        status_code=exc.status_code,
        message=exc.detail
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """请求验证异常处理器"""
    errors = []
    for error in exc.errors():
        field = ".".join(str(loc) for loc in error["loc"][1:])  # 跳过 'body' 前缀
        errors.append(ErrorDetail(
            field=field,
            message=error["msg"]
        ))
    
    return create_error_response(
        status_code=status.HTTP_400_BAD_REQUEST,
        message="请求参数验证失败",
        errors=errors
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    # 在开发环境下打印详细错误信息
    print(f"Unhandled exception: {exc}")
    print(traceback.format_exc())
    
    return create_error_response(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message="服务器内部错误"
    )


# 错误码映射
ERROR_CODE_MAPPING = {
    "INVALID_PROJECT_NAME": "项目名称不符合规范",
    "DUPLICATE_PROJECT_NAME": "项目名称已存在",
    "PROJECT_NOT_FOUND": "项目不存在",
    "INVALID_FILE_TYPE": "文件类型不支持",
    "FILE_TOO_LARGE": "文件大小超限",
    "INVALID_DEPENDENCIES": "依赖格式错误",
    "INVALID_URL": "URL格式错误",
    "INVALID_SELECTOR": "选择器语法错误",
    "STORAGE_ERROR": "文件存储失败",
}
