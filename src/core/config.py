# src/core/config.py (修改部分)
import os
import typing
from pathlib import Path
from typing import List, Dict, Any, ClassVar
from pydantic_settings import BaseSettings
from pydantic import Field, validator


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基本信息
    APP_NAME: str = "项目上传服务 API"
    APP_TITLE: str = "项目上传服务 API"
    PROJECT_NAME: str = "项目上传服务 API"
    APP_DESCRIPTION: str = "提供统一的项目资源管理能力，支持文件、规则、代码三种项目类型的创建和管理"
    APP_VERSION: str = "1.3.0"
    DEBUG: bool = True

    # CORS配置
    CORS_ORIGINS: List[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    # JWT配置
    JWT_SECRET_KEY: str = "auN4sWFbxtnlZywfYmOvLf7QhPrhgXCjOqjRYOpwcDk"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440  # 24小时
    JWT_EXPIRE_HOURS: int = 24

    # 项目路径配置
    PROJECT_ROOT: str = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
    BASE_DIR: str = os.path.abspath(os.path.join(PROJECT_ROOT, os.pardir))

    # 存储配置
    STORAGE_TYPE: str = "local"  # local | s3 | oss
    LOCAL_STORAGE_PATH: str = f"{PROJECT_ROOT}/storage/projects"

    # S3配置（如果使用S3存储）
    AWS_ACCESS_KEY_ID: str = ""
    AWS_SECRET_ACCESS_KEY: str = ""
    AWS_S3_BUCKET: str = ""
    AWS_S3_REGION: str = "us-east-1"

    # 文件限制配置
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_FILE_TYPES: List[str] = [
        '.zip', '.tar.gz', '.py', '.txt', '.json', '.md', '.yml', '.yaml'
    ]

    # ============ Redis配置（修改部分）============
    # Redis连接配置 - 使用URL格式，支持密码认证
    REDIS_URL: str = "redis://:redis_FC5JEz@***********:6379/0"  # 格式: redis://[:password]@host:port/db
    REDIS_TASK_QUEUE: str = "spider:tasks"  # Redis任务队列名称
    REDIS_ENABLED: bool = True  # 是否启用Redis（建议改为True以支持规则任务）

    # Worker标识配置（新增）
    WORKER_ID: str = "Scraper-Node-Default"  # Worker节点标识，用于区分不同的爬虫节点

    @validator("REDIS_URL")
    def validate_redis_url(cls, v):
        """验证Redis URL格式"""
        if not v.startswith(("redis://", "rediss://")):
            raise ValueError("Redis URL must start with redis:// or rediss://")
        return v

    # ============ Redis配置结束 ============

    # 默认管理员配置
    DEFAULT_ADMIN_USERNAME: str = "admin"

    # ============ 调度器配置 ============
    # 任务调度配置
    SCHEDULER_TIMEZONE: str = "Asia/Shanghai"  # 调度器时区
    MAX_CONCURRENT_TASKS: int = 10  # 最大并发任务数

    # 任务执行配置
    TASK_EXECUTION_TIMEOUT: int = 3600  # 默认任务超时时间（秒）
    TASK_MAX_RETRIES: int = 3  # 任务最大重试次数
    TASK_RETRY_DELAY: int = 60  # 重试延迟（秒）
    TASK_LOG_RETENTION_DAYS: int = 30  # 日志保留天数

    # 任务日志配置
    TASK_LOG_DIR: str = f"{PROJECT_ROOT}/logs/tasks"  # 任务日志存储目录
    TASK_LOG_MAX_SIZE: int = 100 * 1024 * 1024  # 单个日志文件最大大小（100MB）

    # 任务执行器配置
    TASK_EXECUTOR_MAX_WORKERS: int = 5  # 执行器最大工作线程数
    TASK_RESULT_EXPIRE_SECONDS: int = 86400  # 任务结果过期时间（24小时）

    # 任务限制配置
    TASK_MAX_INSTANCES_PER_TASK: int = 3  # 每个任务的最大并发实例数
    TASK_MISFIRE_GRACE_TIME: int = 30  # 错过执行的宽限时间（秒）
    TASK_COALESCE: bool = True  # 是否合并错过的执行

    # 任务监控配置
    TASK_MONITOR_INTERVAL: int = 60  # 监控检查间隔（秒）
    TASK_HEALTH_CHECK_ENABLED: bool = True  # 是否启用健康检查

    # 任务存储配置
    TASK_EXECUTION_LOG_DIR: str = f"{PROJECT_ROOT}/storage/executions"  # 执行日志目录
    TASK_RESULT_STORAGE_DIR: str = f"{PROJECT_ROOT}/storage/results"  # 执行结果存储目录

    # ============ 调度器配置结束 ============

    # Tortoise ORM配置
    @property
    def TORTOISE_ORM(self) -> Dict[str, Any]:
        """Tortoise ORM配置 - 使用属性方法避免循环依赖"""
        return {
            "connections": {
                "default": {
                    "engine": "tortoise.backends.sqlite",
                    "credentials": {"file_path": f"{self.BASE_DIR}/antcode.sqlite3"},
                },
            },
            "apps": {
                "models": {
                    "models": ["src.models", "aerich.models"],
                    "default_connection": "default",
                },
            },
            "use_tz": False,
            "timezone": "Asia/Shanghai",
        }

    class Config:
        env_file = ".env"
        case_sensitive = True  # 添加大小写敏感配置


settings = Settings()
