# src/core/init_app.py
import shutil
import os
from aerich import Command
from fastapi import FastAPI
from fastapi.middleware import Middleware
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from tortoise import Tortoise

from src.core.config import settings


def make_middlewares():
    """创建中间件"""
    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=settings.CORS_ORIGINS,
            allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
            allow_methods=settings.CORS_ALLOW_METHODS,
            allow_headers=settings.CORS_ALLOW_HEADERS,
        )
    ]
    return middleware


async def init_db():
    """初始化数据库"""
    command = Command(tortoise_config=settings.TORTOISE_ORM)
    try:
        await command.init_db(safe=True)
    except FileExistsError:
        pass

    await command.init()
    try:
        await command.migrate()
    except AttributeError:
        logger.warning("unable to retrieve model history from database, model history will be created from scratch")
        shutil.rmtree("migrations")
        await command.init_db(safe=True)

    await command.upgrade(run_in_transaction=True)


async def create_default_admin():
    """创建默认管理员用户"""
    from src.models import User
    try:
        admin_exists = await User.filter(username=settings.DEFAULT_ADMIN_USERNAME).exists()
        if not admin_exists:
            admin_user = await User.create_user(
                username="admin",
                password="admin",
                email="<EMAIL>",
                is_admin=True
            )
            logger.info("✅ 默认管理员用户创建成功")
    except Exception as e:
        logger.error(f"创建默认管理员用户失败: {e}")
        raise


async def init_storage():
    """初始化存储目录"""
    try:
        # 创建项目存储目录
        storage_dirs = [
            settings.LOCAL_STORAGE_PATH,
            f"{settings.LOCAL_STORAGE_PATH}/files",
            f"{settings.LOCAL_STORAGE_PATH}/rules",
            f"{settings.LOCAL_STORAGE_PATH}/codes",
            # 任务执行相关目录
            settings.TASK_EXECUTION_LOG_DIR,  # 使用配置中的路径
            settings.TASK_RESULT_STORAGE_DIR,  # 使用配置中的路径
            f"{settings.LOCAL_STORAGE_PATH}/executions",
            f"{settings.LOCAL_STORAGE_PATH}/logs",
        ]

        for dir_path in storage_dirs:
            os.makedirs(dir_path, exist_ok=True)

        logger.info("✅ 存储目录初始化完成")

    except Exception as e:
        logger.error(f"存储目录初始化失败: {e}")
        raise


async def init_redis():
    """初始化Redis连接"""
    if not settings.REDIS_ENABLED:
        logger.info("⚠️ Redis未启用，跳过Redis初始化")
        return

    try:
        from src.services.redis_task_service import redis_task_service

        # 连接Redis
        await redis_task_service.connect()

        # 获取队列信息
        queue_info = await redis_task_service.get_queue_info()
        if queue_info.get("success"):
            logger.info(
                f"📋 Redis队列 '{queue_info.get('queue_name')}' 就绪，当前任务数: {queue_info.get('queue_length', 0)}")

            # 如果有服务器信息，显示内存使用情况
            server_info = queue_info.get("server_info", {})
            if server_info:
                logger.info(f"   内存使用: {server_info.get('memory_used', 'N/A')}")
                logger.info(f"   连接客户端: {server_info.get('connected_clients', 'N/A')}")

        logger.info("✅ Redis服务初始化完成")

    except Exception as e:
        logger.error(f"❌ Redis初始化失败: {e}")
        if settings.REDIS_ENABLED:
            logger.warning("⚠️ Redis配置已启用但连接失败，规则任务功能将不可用")
            logger.warning("   请检查Redis服务是否运行，以及密码是否正确")
        # Redis初始化失败不阻止应用启动，但记录警告


async def shutdown_redis():
    """关闭Redis连接"""
    if not settings.REDIS_ENABLED:
        return

    try:
        from src.services.redis_task_service import redis_task_service
        await redis_task_service.disconnect()
        logger.info("✅ Redis连接已关闭")
    except Exception as e:
        logger.error(f"关闭Redis连接失败: {e}")


async def init_scheduler():
    """初始化调度器"""
    try:
        from src.services.scheduler_service import scheduler_service
        await scheduler_service.start()
        logger.info("✅ 任务调度器启动成功")

        # 如果Redis已启用，调度器将使用Redis任务服务
        if settings.REDIS_ENABLED:
            logger.info("   调度器已配置为使用Redis任务队列")

    except Exception as e:
        logger.error(f"任务调度器启动失败: {e}")
        # 调度器启动失败不影响主应用运行
        logger.warning("⚠️ 应用将在无调度器模式下运行")


async def shutdown_scheduler():
    """关闭调度器"""
    try:
        from src.services.scheduler_service import scheduler_service
        await scheduler_service.shutdown()
        logger.info("✅ 任务调度器已关闭")
    except Exception as e:
        logger.error(f"任务调度器关闭失败: {e}")


async def init_data():
    """初始化应用数据和服务"""
    logger.info("=" * 50)
    logger.info(f"🚀 正在初始化 {settings.APP_NAME} v{settings.APP_VERSION}")
    logger.info("=" * 50)

    # 1. 初始化数据库
    logger.info("📦 步骤 1/5: 初始化数据库...")
    await init_db()

    # 2. 初始化存储目录
    logger.info("📁 步骤 2/5: 初始化存储目录...")
    await init_storage()

    # 3. 创建默认管理员用户
    logger.info("👤 步骤 3/5: 创建默认管理员...")
    await create_default_admin()

    # 4. 初始化Redis（如果启用）
    logger.info("🔌 步骤 4/5: 初始化Redis服务...")
    await init_redis()

    # 5. 初始化调度器
    logger.info("⏰ 步骤 5/5: 初始化任务调度器...")
    await init_scheduler()

    logger.info("=" * 50)
    logger.info(f"✅ {settings.APP_NAME} 初始化完成！")
    logger.info(f"📡 Worker ID: {settings.WORKER_ID}")
    logger.info(f"🌍 时区: {settings.SCHEDULER_TIMEZONE}")
    logger.info("=" * 50)


async def shutdown_services():
    """关闭所有服务（应用关闭时调用）"""
    logger.info("正在关闭应用服务...")

    # 关闭调度器
    await shutdown_scheduler()

    # 关闭Redis连接
    await shutdown_redis()

    # 关闭数据库连接
    await Tortoise.close_connections()

    logger.info("✅ 所有服务已安全关闭")
