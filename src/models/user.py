"""
用户模型定义
"""
from tortoise import fields
from tortoise.models import Model
from passlib.context import CryptContext
from typing import Optional

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class User(Model):
    """用户模型"""

    id = fields.IntField(pk=True, description="用户ID")
    username = fields.CharField(max_length=50, unique=True, description="用户名")
    password_hash = fields.CharField(max_length=128, description="密码哈希")
    email = fields.CharField(max_length=100, null=True, description="邮箱")
    is_active = fields.BooleanField(default=True, description="是否激活")
    is_admin = fields.BooleanField(default=False, description="是否管理员")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    last_login_at = fields.DatetimeField(null=True, description="最后登录时间")

    class Meta:
        table = "users"
        table_description = "用户表"

    def set_password(self, password: str):
        """设置密码"""
        self.password_hash = pwd_context.hash(password)

    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(password, self.password_hash)

    async def update_last_login(self):
        """更新最后登录时间"""
        from datetime import datetime
        self.last_login_at = datetime.now()
        await self.save(update_fields=['last_login_at'])

    @classmethod
    async def authenticate(cls, username: str, password: str) -> Optional['User']:
        """用户认证"""
        user = await cls.filter(username=username, is_active=True).first()
        if not user:
            return None
        if not user.verify_password(password):
            return None
        # 更新登录时间
        await user.update_last_login()
        return user

    @classmethod
    async def create_user(cls, username: str, password: str, email: str = None, is_admin: bool = False) -> 'User':
        """创建用户"""
        user = cls(username=username, email=email, is_admin=is_admin)
        user.set_password(password)
        await user.save()
        return user

    def __str__(self):
        return self.username