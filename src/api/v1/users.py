"""
用户管理API接口
"""
from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import Optional, List
from datetime import datetime
from src.models.user import User
from src.core.auth import get_current_admin_user, get_current_user_id, TokenData, get_current_user
from src.schemas import (
    BaseResponse,
    PaginationResponse,
    UserResponse,
    UserListResponse,
    UserCreateRequest,
    UserUpdateRequest,
    UserPasswordUpdateRequest,
    UserAdminPasswordUpdateRequest
)

router = APIRouter()





@router.get(
    "/",
    response_model=PaginationResponse[UserListResponse],
    summary="获取用户列表",
    description="获取所有用户列表（仅管理员）",
    tags=["用户管理"]
)
async def get_users_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    is_active: Optional[bool] = Query(None, description="是否激活筛选"),
    is_admin: Optional[bool] = Query(None, description="是否管理员筛选"),
    current_admin = Depends(get_current_admin_user)
):
    """获取用户列表（仅管理员可访问）"""

    # 构建查询
    query = User.all()

    # 应用筛选条件
    if is_active is not None:
        query = query.filter(is_active=is_active)

    if is_admin is not None:
        query = query.filter(is_admin=is_admin)

    # 计算总数
    total = await query.count()

    # 分页查询
    offset = (page - 1) * size
    users = await query.offset(offset).limit(size).order_by('-created_at')

    # 转换为响应模型
    user_list = [
        UserListResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            is_active=user.is_active,
            is_admin=user.is_admin,
            created_at=user.created_at,
            last_login_at=user.last_login_at
        )
        for user in users
    ]

    return PaginationResponse(
        data=user_list,
        pagination={
            "page": page,
            "size": size,
            "total": total,
            "pages": (total + size - 1) // size
        }
    )


@router.post(
    "/",
    response_model=BaseResponse[UserResponse],
    status_code=status.HTTP_201_CREATED,
    summary="创建用户",
    description="创建新用户（仅管理员）",
    tags=["用户管理"]
)
async def create_user(
    request: UserCreateRequest,
    current_admin = Depends(get_current_admin_user)
):
    """创建用户（仅管理员可访问）"""

    # 检查用户名是否已存在
    existing_user = await User.filter(username=request.username).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    if request.email:
        existing_email = await User.filter(email=request.email).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="邮箱已存在"
            )

    # 创建用户
    user = await User.create_user(
        username=request.username,
        password=request.password,
        email=request.email,
        is_admin=request.is_admin
    )

    # 构建响应
    response_data = UserResponse(
        id=user.id,
        username=user.username,
        email=user.email,
        is_active=user.is_active,
        is_admin=user.is_admin,
        created_at=user.created_at,
        last_login_at=user.last_login_at
    )

    return BaseResponse(
        success=True,
        code=201,
        message="用户创建成功",
        data=response_data
    )


@router.get(
    "/{user_id}",
    response_model=BaseResponse[UserResponse],
    summary="获取用户详情",
    description="获取指定用户详情",
    tags=["用户管理"]
)
async def get_user_detail(
    user_id: int,
    current_user: TokenData = Depends(get_current_user)
):
    """获取用户详情（管理员可查看所有用户，普通用户只能查看自己）"""

    # 获取当前用户信息
    current_user_obj = await User.get_or_none(id=current_user.user_id)
    if not current_user_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="当前用户不存在"
        )

    # 权限检查：管理员可查看所有用户，普通用户只能查看自己
    if not current_user_obj.is_admin and current_user.user_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只能查看自己的信息"
        )

    # 获取目标用户
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 构建响应
    response_data = UserResponse(
        id=user.id,
        username=user.username,
        email=user.email,
        is_active=user.is_active,
        is_admin=user.is_admin,
        created_at=user.created_at,
        last_login_at=user.last_login_at
    )

    return BaseResponse(
        success=True,
        code=200,
        message="查询成功",
        data=response_data
    )


@router.put(
    "/{user_id}",
    response_model=BaseResponse[UserResponse],
    summary="更新用户信息",
    description="更新用户基本信息",
    tags=["用户管理"]
)
async def update_user(
    user_id: int,
    request: UserUpdateRequest,
    current_user: TokenData = Depends(get_current_user)
):
    """更新用户信息（管理员可更新所有用户，普通用户只能更新自己）"""

    # 获取当前用户信息
    current_user_obj = await User.get_or_none(id=current_user.user_id)
    if not current_user_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="当前用户不存在"
        )

    # 权限检查：管理员可更新所有用户，普通用户只能更新自己
    if not current_user_obj.is_admin and current_user.user_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只能修改自己的信息"
        )

    # 获取目标用户
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 普通用户不能修改管理员权限和激活状态
    if not current_user_obj.is_admin:
        if request.is_admin is not None or request.is_active is not None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="普通用户不能修改权限和状态"
            )

    # 检查邮箱是否已被其他用户使用
    if request.email and request.email != user.email:
        existing_email = await User.filter(email=request.email).exclude(id=user_id).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="邮箱已被其他用户使用"
            )

    # 更新用户信息
    update_data = {}
    if request.email is not None:
        update_data['email'] = request.email
    if request.is_active is not None and current_user_obj.is_admin:
        update_data['is_active'] = request.is_active
    if request.is_admin is not None and current_user_obj.is_admin:
        update_data['is_admin'] = request.is_admin

    if update_data:
        await user.update_from_dict(update_data)
        await user.save()

    # 构建响应
    response_data = UserResponse(
        id=user.id,
        username=user.username,
        email=user.email,
        is_active=user.is_active,
        is_admin=user.is_admin,
        created_at=user.created_at,
        last_login_at=user.last_login_at
    )

    return BaseResponse(
        success=True,
        code=200,
        message="用户信息更新成功",
        data=response_data
    )


@router.put(
    "/{user_id}/password",
    response_model=BaseResponse[None],
    summary="修改用户密码",
    description="修改用户密码",
    tags=["用户管理"]
)
async def update_user_password(
    user_id: int,
    request: UserPasswordUpdateRequest,
    current_user: TokenData = Depends(get_current_user)
):
    """修改用户密码（普通用户需要提供原密码）"""

    # 获取当前用户信息
    current_user_obj = await User.get_or_none(id=current_user.user_id)
    if not current_user_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="当前用户不存在"
        )

    # 权限检查：管理员可修改所有用户密码，普通用户只能修改自己的
    if not current_user_obj.is_admin and current_user.user_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只能修改自己的密码"
        )

    # 获取目标用户
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 普通用户修改自己密码需要验证原密码
    if current_user.user_id == user_id:
        if not user.verify_password(request.old_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="原密码错误"
            )

    # 更新密码
    user.set_password(request.new_password)
    await user.save()

    return BaseResponse(
        success=True,
        code=200,
        message="密码修改成功"
    )


@router.put(
    "/{user_id}/admin-password",
    response_model=BaseResponse[None],
    summary="管理员重置用户密码",
    description="管理员重置用户密码（无需原密码）",
    tags=["用户管理"]
)
async def admin_reset_password(
    user_id: int,
    request: UserAdminPasswordUpdateRequest,
    current_admin = Depends(get_current_admin_user)
):
    """管理员重置用户密码（仅管理员可访问）"""

    # 获取目标用户
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 更新密码
    user.set_password(request.new_password)
    await user.save()

    return BaseResponse(
        success=True,
        code=200,
        message="密码重置成功"
    )


@router.delete(
    "/{user_id}",
    response_model=BaseResponse[None],
    summary="删除用户",
    description="删除指定用户（仅管理员）",
    tags=["用户管理"]
)
async def delete_user(
    user_id: int,
    current_admin = Depends(get_current_admin_user)
):
    """删除用户（仅管理员可访问）"""

    # 获取目标用户
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 不能删除自己
    if user_id == current_admin.user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己"
        )

    # 检查是否是最后一个管理员
    if user.is_admin:
        admin_count = await User.filter(is_admin=True).count()
        if admin_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除最后一个管理员"
            )

    # 删除用户
    await user.delete()

    return BaseResponse(
        success=True,
        code=200,
        message="用户删除成功"
    )