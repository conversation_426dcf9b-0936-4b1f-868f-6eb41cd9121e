"""
基础API接口
包含认证、健康检查等基础功能
"""
from fastapi import APIRouter, HTTPException, status
from typing import Optional
from datetime import datetime
from src.models.user import User
from src.core.auth import jwt_auth
from src.core.config import settings
from src.schemas import HealthResponse, UserLoginRequest, UserLoginResponse

router = APIRouter()








@router.get(
    "/health",
    response_model=HealthResponse,
    summary="健康检查",
    description="检查服务运行状态和基本信息",
    tags=["基础功能"]
)
async def health_check():
    """健康检查接口"""
    from datetime import datetime

    return HealthResponse(
        status="healthy",
        version=settings.APP_VERSION,
        timestamp=datetime.now().isoformat()
    )


@router.post(
    "/auth/login",
    response_model=UserLoginResponse,
    summary="用户登录",
    description="用户登录验证，返回JWT访问令牌",
    response_description="返回JWT令牌和用户基本信息",
    tags=["认证"]
)
async def login(request: UserLoginRequest):
    """用户登录"""
    # 验证用户凭据
    user = await User.authenticate(request.username, request.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户账号已被禁用"
        )

    # 创建访问令牌
    access_token = jwt_auth.create_access_token(
        user_id=user.id,
        username=user.username
    )

    return UserLoginResponse(
        access_token=access_token,
        user_id=user.id,
        username=user.username,
        is_admin=user.is_admin
    )


