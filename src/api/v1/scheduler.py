# src/api/v1/scheduler.py
"""任务调度API接口"""
import json
from typing import List, Optional
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, Query, status, WebSocket, WebSocketDisconnect
from tortoise.exceptions import DoesNotExist, IntegrityError
from loguru import logger

from src.models.scheduler import ScheduledTask, TaskExecution, TaskLog
from src.models.project import Project
from src.models.user import User
from src.models.enums import TaskStatus, ProjectType
from src.schemas.scheduler import (
    TaskCreate, TaskUpdate, TaskResponse,
    ExecutionResponse, ExecutionLogResponse, LogFileResponse,
    TaskStatsResponse, SystemMetricsResponse,
    TaskListResponse, ExecutionListResponse
)
from src.schemas.common import BaseResponse
from src.core.auth import get_current_user, TokenData
from src.services.scheduler_service import scheduler_service
from src.services.task_log_service import task_log_service
from src.services.websocket_log_service import websocket_log_manager

router = APIRouter(prefix="/scheduler", tags=["任务调度"])


@router.post("/tasks", response_model=BaseResponse[TaskResponse])
async def create_task(
        task_data: TaskCreate,
        current_user: TokenData = Depends(get_current_user)
):
    """创建调度任务"""
    try:
        # 获取用户对象
        user = await User.get(id=current_user.user_id)

        # 检查项目是否存在且属于当前用户
        project = await Project.get(
            id=task_data.project_id,
            user=user
        ).prefetch_related('file_detail', 'code_detail', 'rule_detail')
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在或无权访问"
        )

    try:
        # 创建任务
        task = await ScheduledTask.create(
            **task_data.model_dump(exclude={'project_id'}),
            project=project,
            task_type=ProjectType(project.type),
            user=user
        )

        # 添加到调度器
        if task.is_active:
            await scheduler_service.add_task(task)

        return BaseResponse(
            success=True,
            code=200,
            message="任务创建成功",
            data=TaskResponse.from_orm(task)
        )
    except IntegrityError:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="任务名称已存在"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/tasks", response_model=TaskListResponse)
async def list_tasks(
        page: int = Query(1, ge=1),
        size: int = Query(20, ge=1, le=100),
        status: Optional[TaskStatus] = None,
        is_active: Optional[bool] = None,
        current_user: TokenData = Depends(get_current_user)
):
    """获取任务列表"""
    query = ScheduledTask.filter(user_id=current_user.user_id)

    if status:
        query = query.filter(status=status)
    if is_active is not None:
        query = query.filter(is_active=is_active)

    total = await query.count()
    tasks = await query.offset((page - 1) * size).limit(size).order_by('-created_at')

    return TaskListResponse(
        total=total,
        page=page,
        size=size,
        items=[TaskResponse.from_orm(task) for task in tasks]
    )


@router.get("/tasks/{task_id}", response_model=BaseResponse[TaskResponse])
async def get_task(
        task_id: int,
        current_user: TokenData = Depends(get_current_user)
):
    """获取任务详情"""
    try:
        task = await ScheduledTask.get(
            id=task_id,
            user_id=current_user.user_id
        ).prefetch_related('project')

        return BaseResponse(
            success=True,
            code=200,
            message="获取成功",
            data=TaskResponse.from_orm(task)
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )


@router.put("/tasks/{task_id}", response_model=BaseResponse[TaskResponse])
async def update_task(
        task_id: int,
        task_data: TaskUpdate,
        current_user: TokenData = Depends(get_current_user)
):
    """更新任务"""
    try:
        task = await ScheduledTask.get(
            id=task_id,
            user_id=current_user.user_id
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 更新任务
    update_data = task_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(task, field, value)
    await task.save()

    # 重新加载到调度器
    if task.is_active:
        await scheduler_service.remove_task(task_id)
        await scheduler_service.add_task(task)

    return BaseResponse(
        success=True,
        code=200,
        message="任务更新成功",
        data=TaskResponse.from_orm(task)
    )


@router.delete("/tasks/{task_id}", response_model=BaseResponse)
async def delete_task(
        task_id: int,
        current_user: TokenData = Depends(get_current_user)
):
    """删除任务"""
    try:
        task = await ScheduledTask.get(
            id=task_id,
            user_id=current_user.user_id
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 从调度器移除
    try:
        await scheduler_service.remove_task(task_id)
    except:
        pass

    # 删除任务
    await task.delete()

    return BaseResponse(
        success=True,
        code=200,
        message="任务删除成功",
        data=None
    )


@router.post("/tasks/{task_id}/pause", response_model=BaseResponse)
async def pause_task(
        task_id: int,
        current_user: TokenData = Depends(get_current_user)
):
    """暂停任务"""
    try:
        task = await ScheduledTask.get(
            id=task_id,
            user_id=current_user.user_id
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    await scheduler_service.pause_task(task_id)

    return BaseResponse(
        success=True,
        code=200,
        message="任务已暂停",
        data=None
    )


@router.post("/tasks/{task_id}/resume", response_model=BaseResponse)
async def resume_task(
        task_id: int,
        current_user: TokenData = Depends(get_current_user)
):
    """恢复任务"""
    try:
        task = await ScheduledTask.get(
            id=task_id,
            user_id=current_user.user_id
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    await scheduler_service.resume_task(task_id)

    return BaseResponse(
        success=True,
        code=200,
        message="任务已恢复",
        data=None
    )


@router.post("/tasks/{task_id}/trigger", response_model=BaseResponse)
async def trigger_task(
        task_id: int,
        current_user: TokenData = Depends(get_current_user)
):
    """立即触发任务"""
    try:
        task = await ScheduledTask.get(
            id=task_id,
            user_id=current_user.user_id
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    await scheduler_service.trigger_task(task_id)

    return BaseResponse(
        success=True,
        code=200,
        message="任务已触发",
        data=None
    )


@router.get("/tasks/{task_id}/executions", response_model=ExecutionListResponse)
async def list_task_executions(
        task_id: int,
        page: int = Query(1, ge=1),
        size: int = Query(20, ge=1, le=100),
        status: Optional[TaskStatus] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        current_user: TokenData = Depends(get_current_user)
):
    """获取任务执行历史"""
    # 验证任务权限
    try:
        await ScheduledTask.get(
            id=task_id,
            user_id=current_user.user_id
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    query = TaskExecution.filter(task_id=task_id)

    if status:
        query = query.filter(status=status)
    if start_date:
        query = query.filter(start_time__gte=start_date)
    if end_date:
        query = query.filter(start_time__lte=end_date)

    total = await query.count()
    executions = await query.offset((page - 1) * size).limit(size).order_by('-start_time')

    return ExecutionListResponse(
        total=total,
        page=page,
        size=size,
        items=[ExecutionResponse.from_orm(e) for e in executions]
    )


@router.get("/executions/{execution_id}", response_model=BaseResponse[ExecutionResponse])
async def get_execution(
        execution_id: str,
        current_user: TokenData = Depends(get_current_user)
):
    """获取执行详情"""
    try:
        execution = await TaskExecution.get(
            execution_id=execution_id
        ).prefetch_related('task')

        # 验证权限
        if execution.task.user_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问"
            )

        return BaseResponse(
            success=True,
            code=200,
            message="获取成功",
            data=ExecutionResponse.from_orm(execution)
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="执行记录不存在"
        )


@router.get("/executions/{execution_id}/logs", response_model=BaseResponse[List[ExecutionLogResponse]])
async def get_execution_logs(
        execution_id: str,
        level: Optional[str] = None,
        current_user: TokenData = Depends(get_current_user)
):
    """获取执行日志（数据库记录）"""
    try:
        execution = await TaskExecution.get(
            execution_id=execution_id
        ).prefetch_related('task')

        # 验证权限
        if execution.task.user_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问"
            )

        query = TaskLog.filter(execution=execution)
        if level:
            query = query.filter(level=level)

        logs = await query.order_by('timestamp').all()

        return BaseResponse(
            success=True,
            code=200,
            message="获取成功",
            data=[ExecutionLogResponse.from_orm(log) for log in logs]
        )

    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="执行记录不存在"
        )


@router.get("/executions/{execution_id}/logs/file", response_model=BaseResponse[LogFileResponse])
async def get_execution_log_file(
        execution_id: str,
        log_type: str = Query("output", regex="^(output|error)$"),
        lines: Optional[int] = Query(None, ge=1, le=10000),
        current_user: TokenData = Depends(get_current_user)
):
    """获取执行日志文件内容"""
    try:
        execution = await TaskExecution.get(
            execution_id=execution_id
        ).prefetch_related('task')

        # 验证权限
        if execution.task.user_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问"
            )

        # 确定日志文件路径
        if log_type == "output":
            log_file_path = execution.log_file_path
        else:  # error
            log_file_path = execution.error_log_path

        if not log_file_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日志文件路径不存在"
            )

        # 读取日志文件内容
        content = await task_log_service.read_log(log_file_path, lines)
        log_info = await task_log_service.get_log_info(log_file_path)

        return BaseResponse(
            success=True,
            code=200,
            message="获取成功",
            data=LogFileResponse(
                execution_id=execution_id,
                log_type=log_type,
                content=content,
                file_path=log_file_path,
                file_size=log_info.get("size", 0),
                lines_count=log_info.get("lines", 0),
                last_modified=log_info.get("modified_time")
            )
        )

    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="执行记录不存在"
        )


@router.get("/tasks/{task_id}/stats", response_model=BaseResponse[TaskStatsResponse])
async def get_task_stats(
        task_id: int,
        current_user: TokenData = Depends(get_current_user)
):
    """获取任务统计信息"""
    try:
        await ScheduledTask.get(
            id=task_id,
            user_id=current_user.user_id
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    executions = await TaskExecution.filter(task_id=task_id).all()

    total = len(executions)
    success = sum(1 for e in executions if e.status == TaskStatus.SUCCESS)
    failed = sum(1 for e in executions if e.status == TaskStatus.FAILED)

    avg_duration = 0
    if executions:
        durations = [e.duration_seconds for e in executions if e.duration_seconds]
        if durations:
            avg_duration = sum(durations) / len(durations)

    stats = TaskStatsResponse(
        total_executions=total,
        success_count=success,
        failed_count=failed,
        success_rate=success / total if total > 0 else 0,
        average_duration=avg_duration
    )

    return BaseResponse(
        success=True,
        code=200,
        message="获取成功",
        data=stats
    )


@router.get("/metrics", response_model=BaseResponse[SystemMetricsResponse])
async def get_system_metrics(
        current_user: TokenData = Depends(get_current_user)
):
    """获取系统指标"""
    try:
        import psutil

        metrics = SystemMetricsResponse(
            cpu_percent=psutil.cpu_percent(interval=1),
            memory_percent=psutil.virtual_memory().percent,
            disk_usage=psutil.disk_usage('/').percent,
            active_tasks=len(scheduler_service.running_tasks)
        )

        return BaseResponse(
            success=True,
            code=200,
            message="获取成功",
            data=metrics
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统指标失败: {str(e)}"
        )


@router.get("/running", response_model=BaseResponse[List[dict]])
async def get_running_tasks(
        current_user: TokenData = Depends(get_current_user)
):
    """获取运行中的任务"""
    running = scheduler_service.get_running_tasks()

    # 过滤只显示当前用户的任务
    user = await User.get(id=current_user.user_id)
    user_tasks = []
    for task_info in running:
        task = await ScheduledTask.get_or_none(id=task_info['task_id'])
        if task and task.user_id == user.id:
            user_tasks.append(task_info)

    return BaseResponse(
        success=True,
        code=200,
        message="获取成功",
        data=user_tasks
    )


@router.websocket("/executions/{execution_id}/logs/ws")
async def websocket_logs(websocket: WebSocket, execution_id: str):
    """
    WebSocket实时日志查看端点

    Args:
        websocket: WebSocket连接对象
        execution_id: 任务执行ID
    """
    try:
        # 从查询参数获取token
        query_params = dict(websocket.query_params)
        token = query_params.get('token')

        if not token:
            await websocket.close(code=4001, reason="缺少认证令牌")
            return

        # 验证JWT令牌
        from src.core.auth import jwt_auth
        token_data = jwt_auth.verify_token(token)
        user_id = token_data.user_id

        # 验证执行记录权限
        try:
            execution = await TaskExecution.get(
                execution_id=execution_id
            ).prefetch_related('task')

            if execution.task.user_id != user_id:
                await websocket.close(code=4003, reason="无权访问")
                return

        except DoesNotExist:
            await websocket.close(code=4004, reason="执行记录不存在")
            return

        # 建立WebSocket连接
        connection_id = await websocket_log_manager.connect(websocket, execution_id, user_id)

        try:
            # 发送历史日志
            await websocket_log_manager.send_historical_logs(websocket, execution_id)

            # 保持连接并处理客户端消息
            while True:
                try:
                    # 接收客户端消息
                    data = await websocket.receive_text()
                    message = json.loads(data)

                    # 处理客户端请求
                    if message.get("type") == "ping":
                        await websocket.send_text(json.dumps({
                            "type": "pong",
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }))
                    elif message.get("type") == "get_stats":
                        # 发送连接统计信息
                        stats = websocket_log_manager.get_connection_stats()
                        await websocket.send_text(json.dumps({
                            "type": "stats",
                            "data": stats,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }))

                except WebSocketDisconnect:
                    break
                except Exception as e:
                    logger.error(f"处理WebSocket消息失败: {e}")
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": f"处理消息失败: {e}",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }))

        except WebSocketDisconnect:
            pass
        finally:
            # 清理连接
            await websocket_log_manager.disconnect(connection_id)

    except Exception as e:
        logger.error(f"WebSocket连接失败: {e}")
        try:
            await websocket.close(code=4000, reason=f"连接失败: {e}")
        except:
            pass


@router.get("/websocket/stats", response_model=BaseResponse[dict])
async def get_websocket_stats(
        current_user: TokenData = Depends(get_current_user)
):
    """获取WebSocket连接统计信息"""
    stats = websocket_log_manager.get_connection_stats()

    return BaseResponse(
        success=True,
        code=200,
        message="获取成功",
        data=stats
    )
