"""
项目相关API接口
处理项目的创建、查询、更新、删除等操作
"""

from typing import Optional, List
from fastapi import APIRouter, Depends, Form, File, UploadFile, Query, status, HTTPException
from fastapi.responses import JSONResponse

from src.core.auth import get_current_user_id
from src.models.enums import ProjectType, ProjectStatus
from src.schemas.project import (
    ProjectCreateRequest, ProjectRuleCreateRequest, ProjectFileCreateRequest, ProjectCodeCreateRequest,
    ProjectResponse, ProjectListResponse, ProjectUpdateRequest,
    ProjectCreateFormRequest, ProjectListQueryRequest, TaskJsonRequest, ProjectRuleUpdateRequest
)
from src.schemas.common import BaseResponse, PaginationParams, PaginationResponse, PaginationInfo
from src.services.project_service import project_service
from src.core.exceptions import ProjectNotFoundException

project_router = APIRouter()


async def get_project_create_form(
    # 通用参数
    name: str = Form(..., min_length=3, max_length=50, description="项目名称"),
    description: Optional[str] = Form(None, max_length=500, description="项目描述"),
    type: ProjectType = Form(..., description="项目类型"),
    tags: Optional[str] = Form(None, description="项目标签，逗号分隔或JSON数组"),
    dependencies: Optional[str] = Form(None, description="Python依赖包JSON数组"),

    # 文件项目参数
    entry_point: Optional[str] = Form(None, max_length=255, description="入口文件路径"),
    runtime_config: Optional[str] = Form(None, description="运行时配置JSON"),
    environment_vars: Optional[str] = Form(None, description="环境变量JSON"),

    # 规则项目参数
    engine: Optional[str] = Form("requests", description="采集引擎 (browser/requests/curl_cffi)"),
    target_url: Optional[str] = Form(None, max_length=2000, description="目标URL"),
    url_pattern: Optional[str] = Form(None, max_length=500, description="URL匹配模式"),
    list_selectors: Optional[str] = Form(None, description="列表页选择器JSON"),
    detail_selectors: Optional[str] = Form(None, description="详情页选择器JSON"),
    pagination_type: Optional[str] = Form(None, description="翻页类型"),
    pagination_rule: Optional[str] = Form(None, max_length=500, description="翻页规则"),
    max_pages: Optional[int] = Form(10, ge=1, le=1000, description="最大页数"),
    request_delay: Optional[int] = Form(1000, ge=0, description="请求间隔(ms)"),

    # 代码项目参数
    language: Optional[str] = Form("python", max_length=50, description="编程语言"),
    version: Optional[str] = Form("1.0.0", max_length=20, description="版本号"),
    code_entry_point: Optional[str] = Form(None, max_length=255, description="入口函数"),
    documentation: Optional[str] = Form(None, description="代码文档"),
) -> ProjectCreateFormRequest:
    """获取项目创建Form参数的依赖函数"""
    return ProjectCreateFormRequest(
        name=name,
        description=description,
        type=type,
        tags=tags,
        dependencies=dependencies,
        entry_point=entry_point,
        runtime_config=runtime_config,
        environment_vars=environment_vars,
        engine=engine,
        target_url=target_url,
        url_pattern=url_pattern,
        list_selectors=list_selectors,
        detail_selectors=detail_selectors,
        pagination_type=pagination_type,
        pagination_rule=pagination_rule,
        max_pages=max_pages,
        request_delay=request_delay,
        language=language,
        version=version,
        code_entry_point=code_entry_point,
        documentation=documentation,
    )


async def get_project_list_query(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    type: Optional[ProjectType] = Query(None, description="项目类型筛选"),
    status: Optional[ProjectStatus] = Query(None, description="项目状态筛选"),
    tag: Optional[str] = Query(None, description="标签筛选"),
) -> ProjectListQueryRequest:
    """获取项目列表查询参数的依赖函数"""
    return ProjectListQueryRequest(
        page=page,
        size=size,
        type=type,
        status=status,
        tag=tag,
    )


@project_router.post(
    "/",
    response_model=BaseResponse[ProjectResponse],
    status_code=status.HTTP_201_CREATED,
    summary="创建项目",
    description="创建新项目，支持文件、规则、代码三种类型",
    response_description="返回创建的项目信息"
)
async def create_project(
    form_data: ProjectCreateFormRequest = Depends(get_project_create_form),
    file: Optional[UploadFile] = File(None, description="项目文件（文件项目必需）"),
    code_file: Optional[UploadFile] = File(None, description="代码文件（代码项目必需）"),
    current_user_id: int = Depends(get_current_user_id)
):
    """创建项目"""

    # 构建请求数据
    request_data = {
        "name": form_data.name,
        "description": form_data.description,
        "type": form_data.type,
        "tags": form_data.tags,
        "dependencies": form_data.dependencies,
    }

    # 根据项目类型添加特定参数
    if form_data.type == ProjectType.FILE:
        request_data.update({
            "entry_point": form_data.entry_point,
            "runtime_config": form_data.runtime_config,
            "environment_vars": form_data.environment_vars,
        })
    elif form_data.type == ProjectType.RULE:
        # 验证规则项目必需字段
        if not form_data.target_url:
            raise HTTPException(status_code=400, detail="规则项目必须提供target_url")
        if not form_data.detail_selectors:
            raise HTTPException(status_code=400, detail="规则项目必须提供detail_selectors")

        request_data.update({
            "engine": form_data.engine,
            "target_url": form_data.target_url,
            "url_pattern": form_data.url_pattern,
            "list_selectors": form_data.list_selectors,
            "detail_selectors": form_data.detail_selectors,
            "pagination_type": form_data.pagination_type,
            "pagination_rule": form_data.pagination_rule,
            "max_pages": form_data.max_pages,
            "request_delay": form_data.request_delay,
        })
    elif form_data.type == ProjectType.CODE:
        request_data.update({
            "language": form_data.language,
            "version": form_data.version,
            "entry_point": form_data.code_entry_point,
            "documentation": form_data.documentation,
        })

    # 根据项目类型创建不同的请求对象
    if form_data.type == ProjectType.RULE:
        request = ProjectRuleCreateRequest(**request_data)
    elif form_data.type == ProjectType.FILE:
        request = ProjectFileCreateRequest(**request_data)
    elif form_data.type == ProjectType.CODE:
        request = ProjectCodeCreateRequest(**request_data)
    else:
        request = ProjectCreateRequest(**request_data)

    # 创建项目
    project = await project_service.create_project(
        request=request,
        user_id=current_user_id,
        file=file,
        code_file=code_file
    )

    # 构建响应数据
    response_data = ProjectResponse.from_orm(project)

    # 如果是文件项目，添加文件信息
    if project.type == ProjectType.FILE and hasattr(project, 'file_detail'):
        file_detail = project.file_detail
        response_data.file_info = {
            "original_name": file_detail.original_name,
            "file_size": file_detail.file_size,
            "file_hash": file_detail.file_hash
        }

    return BaseResponse(
        success=True,
        code=201,
        message="项目创建成功",
        data=response_data
    )


@project_router.get(
    "/",
    response_model=PaginationResponse[ProjectListResponse],
    summary="获取项目列表",
    description="获取当前用户的项目列表，支持分页和筛选",
    response_description="返回项目列表和分页信息"
)
async def get_projects_list(
    query_params: ProjectListQueryRequest = Depends(get_project_list_query),
    current_user_id: int = Depends(get_current_user_id)
):

    # 查询项目列表
    projects, total = await project_service.get_projects_list(
        page=query_params.page,
        size=query_params.size,
        project_type=query_params.type,
        status=query_params.status.value if query_params.status else None,
        tag=query_params.tag,
        user_id=current_user_id
    )

    # 转换为响应格式
    project_list = [ProjectListResponse.from_orm(project) for project in projects]

    # 计算分页信息
    pages = (total + query_params.size - 1) // query_params.size
    pagination_info = PaginationInfo(
        page=query_params.page,
        size=query_params.size,
        total=total,
        pages=pages
    )

    return PaginationResponse(
        success=True,
        code=200,
        message="查询成功",
        data=project_list,
        pagination=pagination_info
    )


@project_router.get(
    "/{project_id}",
    response_model=BaseResponse[ProjectResponse],
    summary="获取项目详情",
    description="根据项目ID获取项目详细信息",
    response_description="返回项目详细信息"
)
async def get_project_detail(
    project_id: int,
    current_user_id: int = Depends(get_current_user_id)
):

    # 查询项目
    project = await project_service.get_project_by_id(project_id, current_user_id)
    if not project:
        raise ProjectNotFoundException(project_id)

    # 构建响应数据
    response_data = ProjectResponse.from_orm(project)

    # 根据项目类型添加详细信息
    if project.type == ProjectType.FILE and hasattr(project, 'file_detail'):
        file_detail = project.file_detail
        response_data.file_info = {
            "original_name": file_detail.original_name,
            "file_size": file_detail.file_size,
            "file_hash": file_detail.file_hash
        }

    return BaseResponse(
        success=True,
        code=200,
        message="查询成功",
        data=response_data
    )


@project_router.put(
    "/{project_id}",
    response_model=BaseResponse[ProjectResponse],
    summary="更新项目",
    description="更新指定项目的基本信息",
    response_description="返回更新后的项目信息"
)
async def update_project(
    project_id: int,
    request: ProjectUpdateRequest,
    current_user_id: int = Depends(get_current_user_id)
):
    """更新项目"""

    # 更新项目
    project = await project_service.update_project(project_id, request, current_user_id)
    if not project:
        raise ProjectNotFoundException(project_id)

    # 构建响应数据
    response_data = ProjectResponse.from_orm(project)

    return BaseResponse(
        success=True,
        code=200,
        message="项目更新成功",
        data=response_data
    )


@project_router.delete(
    "/{project_id}",
    response_model=BaseResponse[None],
    summary="删除项目",
    description="删除指定的项目（不可逆操作）",
    response_description="返回删除操作结果"
)
async def delete_project(
    project_id: int,
    current_user_id: int = Depends(get_current_user_id)
):

    # 删除项目
    success = await project_service.delete_project(project_id, current_user_id)
    if not success:
        raise ProjectNotFoundException(project_id)

    return BaseResponse(
        success=True,
        code=200,
        message="项目删除成功",
        data=None
    )


@project_router.get(
    "/{project_id}/task-json",
    response_model=BaseResponse[TaskJsonRequest],
    summary="生成任务JSON",
    description="根据规则项目配置生成爬虫任务JSON",
    response_description="返回任务JSON配置"
)
async def generate_task_json(
    project_id: int,
    current_user_id: int = Depends(get_current_user_id)
):
    """生成任务JSON"""

    # 获取项目详情
    project = await project_service.get_project_detail(project_id, current_user_id)
    if not project:
        raise ProjectNotFoundException(project_id)

    # 检查项目类型
    if project.type != ProjectType.RULE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只有规则项目才能生成任务JSON"
        )

    # 获取规则详情
    rule_detail = await project.rule_detail
    if not rule_detail:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="规则项目配置不完整"
        )

    # 构建任务JSON
    task_json = await project_service.generate_task_json(rule_detail)

    return BaseResponse(
        success=True,
        code=200,
        message="任务JSON生成成功",
        data=task_json
    )


@project_router.put(
    "/{project_id}/rule-config",
    response_model=BaseResponse[ProjectResponse],
    summary="更新规则项目配置",
    description="更新规则项目的详细配置",
    response_description="返回更新后的项目信息"
)
async def update_rule_config(
    project_id: int,
    request: ProjectRuleUpdateRequest,
    current_user_id: int = Depends(get_current_user_id)
):
    """更新规则项目配置"""

    # 获取项目详情
    project = await project_service.get_project_detail(project_id, current_user_id)
    if not project:
        raise ProjectNotFoundException(project_id)

    # 检查项目类型
    if project.type != ProjectType.RULE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只有规则项目才能更新规则配置"
        )

    # 更新规则配置
    updated_project = await project_service.update_rule_config(project_id, request, current_user_id)

    # 构建响应数据
    response_data = ProjectResponse.from_orm(updated_project)

    return BaseResponse(
        success=True,
        code=200,
        message="规则配置更新成功",
        data=response_data
    )