from fastapi import APIRouter
from .project import project_router
from .base import router as base_router
from .users import router as users_router
from .scheduler import router as scheduler_router

v1_router = APIRouter()

v1_router.include_router(base_router)
v1_router.include_router(users_router, prefix="/users", tags=["用户管理"])
v1_router.include_router(project_router, prefix="/projects", tags=["项目管理"])
v1_router.include_router(scheduler_router)
__all__ = ["v1_router"]