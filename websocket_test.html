<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket实时日志测试</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background-color: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .controls {
            background-color: #2d2d30;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .log-container {
            background-color: #0d1117;
            border: 1px solid #30363d;
            border-radius: 8px;
            height: 500px;
            overflow-y: auto;
            padding: 15px;
            font-size: 14px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-info { color: #58a6ff; }
        .log-warning { color: #f85149; }
        .log-error { color: #ff6b6b; }
        .log-success { color: #56d364; }
        .log-timestamp { color: #8b949e; }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-connected { background-color: #238636; color: white; }
        .status-disconnected { background-color: #da3633; color: white; }
        .status-connecting { background-color: #fb8500; color: white; }
        input, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #30363d;
            border-radius: 4px;
            background-color: #21262d;
            color: #d4d4d4;
        }
        button {
            background-color: #238636;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #2ea043;
        }
        button:disabled {
            background-color: #6e7681;
            cursor: not-allowed;
        }
        .stats {
            background-color: #161b22;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 WebSocket实时日志测试</h1>
            <p>测试任务执行日志的实时推送功能</p>
        </div>

        <div class="controls">
            <label>执行ID:</label>
            <input type="text" id="executionId" placeholder="输入任务执行ID" value="ae92f6af-48ba-4d7b-a211-e1f90a101218">
            
            <label>JWT Token:</label>
            <input type="text" id="token" placeholder="输入JWT令牌" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwiZXhwIjoxNzU1NTE0ODk5fQ.gS58uCti_F0iF-6XiKZ1RpqOQOJzb1-g-zwq81YsqA8">
            
            <br>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            <button onclick="sendPing()">发送Ping</button>
            <button onclick="getStats()">获取统计</button>
            <button onclick="clearLogs()">清空日志</button>
            
            <div class="stats">
                状态: <span id="status" class="status status-disconnected">未连接</span>
                | 连接ID: <span id="connectionId">-</span>
                | 消息数: <span id="messageCount">0</span>
            </div>
        </div>

        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">
                <span class="log-timestamp">[等待连接]</span> 请输入执行ID和Token，然后点击连接按钮
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        let connectionId = null;

        function updateStatus(status, text) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status status-${status}`;
            statusEl.textContent = text;
        }

        function addLogEntry(type, message, timestamp = null) {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            
            const time = timestamp || new Date().toISOString();
            entry.innerHTML = `<span class="log-timestamp">[${time}]</span> ${message}`;
            
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
            
            messageCount++;
            document.getElementById('messageCount').textContent = messageCount;
        }

        function connect() {
            const executionId = document.getElementById('executionId').value.trim();
            const token = document.getElementById('token').value.trim();
            
            if (!executionId || !token) {
                addLogEntry('error', '请输入执行ID和Token');
                return;
            }

            if (ws) {
                ws.close();
            }

            updateStatus('connecting', '连接中...');
            addLogEntry('info', `正在连接到执行ID: ${executionId}`);

            const wsUrl = `ws://localhost:8000/api/v1/scheduler/executions/${executionId}/logs/ws?token=${encodeURIComponent(token)}`;
            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                updateStatus('connected', '已连接');
                addLogEntry('success', 'WebSocket连接已建立');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
            };

            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (e) {
                    addLogEntry('error', `解析消息失败: ${e.message}`);
                }
            };

            ws.onclose = function(event) {
                updateStatus('disconnected', '已断开');
                addLogEntry('warning', `WebSocket连接已关闭 (代码: ${event.code}, 原因: ${event.reason || '未知'})`);
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                document.getElementById('connectionId').textContent = '-';
            };

            ws.onerror = function(error) {
                addLogEntry('error', `WebSocket错误: ${error.message || '连接失败'}`);
            };
        }

        function handleMessage(data) {
            switch (data.type) {
                case 'connection_established':
                    connectionId = data.connection_id;
                    document.getElementById('connectionId').textContent = connectionId;
                    addLogEntry('success', `连接已建立，连接ID: ${connectionId}`);
                    break;
                
                case 'historical_log':
                    addLogEntry('info', `[历史] ${data.data.message}`, data.timestamp);
                    break;
                
                case 'log_message':
                    const logType = data.data.level.toLowerCase();
                    addLogEntry(logType, `[实时] ${data.data.message}`, data.timestamp);
                    break;
                
                case 'execution_status':
                    const status = data.data.status.toLowerCase();
                    addLogEntry(status === 'success' ? 'success' : 'warning', 
                              `[状态] ${data.data.message}`, data.timestamp);
                    break;
                
                case 'pong':
                    addLogEntry('info', '收到Pong响应', data.timestamp);
                    break;
                
                case 'stats':
                    addLogEntry('info', `连接统计: ${JSON.stringify(data.data, null, 2)}`);
                    break;
                
                case 'error':
                    addLogEntry('error', `服务器错误: ${data.message}`, data.timestamp);
                    break;
                
                default:
                    addLogEntry('info', `未知消息类型: ${JSON.stringify(data)}`);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendPing() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'ping' }));
                addLogEntry('info', '发送Ping消息');
            } else {
                addLogEntry('error', 'WebSocket未连接');
            }
        }

        function getStats() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'get_stats' }));
                addLogEntry('info', '请求连接统计');
            } else {
                addLogEntry('error', 'WebSocket未连接');
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            messageCount = 0;
            document.getElementById('messageCount').textContent = '0';
        }

        // 页面加载时的初始化
        window.onload = function() {
            addLogEntry('info', 'WebSocket测试页面已加载');
        };

        // 页面关闭时清理连接
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html>
