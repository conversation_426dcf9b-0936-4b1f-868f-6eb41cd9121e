[project]
name = "antcode"
version = "0.1.0"
description = "项目上传服务 API - 提供统一的项目资源管理能力"
requires-python = ">=3.11"
dependencies = [
    "fastapi==0.116.1",
    "loguru>=0.7.3",
    "pydantic-settings>=2.0.0",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.1",
    "python-multipart>=0.0.20",
    "tortoise-orm>=0.25.1",
    "uvicorn>=0.35.0",
    "httpx>=0.25.0",
    "passlib[bcrypt]>=1.7.4",
    "aerich>=0.9.1",
    "apscheduler>=3.11.0",
    "email-validator>=2.2.0",
    "redis>=6.4.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "faker>=37.5.3",
    "websockets>=15.0.1",
    "psutil>=7.0.0",
]



